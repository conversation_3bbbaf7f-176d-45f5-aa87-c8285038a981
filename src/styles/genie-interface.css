/* Genie Interface Styles */
.genie-interface {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    font-family: 'Arial', sans-serif;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.genie-interface.hidden {
    opacity: 0;
    visibility: hidden;
    pointer-events: none;
}

.genie-interface.visible {
    opacity: 1;
    visibility: visible;
    pointer-events: all;
}

/* Modal Structure */
.genie-modal {
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.genie-backdrop {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle at center, rgba(138, 43, 226, 0.3), rgba(0, 0, 0, 0.8));
    backdrop-filter: blur(5px);
}

.genie-content {
    position: relative;
    max-width: 900px;
    max-height: 90vh;
    width: 90%;
    background: linear-gradient(135deg, #1a0537 0%, #2d1a05 50%, #1a0537 100%);
    border: 3px solid #ffd700;
    border-radius: 20px;
    box-shadow: 
        0 0 30px rgba(255, 215, 0, 0.5),
        inset 0 0 20px rgba(255, 215, 0, 0.1);
    overflow-y: auto;
    animation: genieAppear 0.5s ease-out;
}

@keyframes genieAppear {
    from {
        transform: scale(0.8) translateY(50px);
        opacity: 0;
    }
    to {
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Header */
.genie-header {
    text-align: center;
    padding: 30px 20px 20px;
    border-bottom: 2px solid rgba(255, 215, 0, 0.3);
}

.genie-character {
    position: relative;
    margin-bottom: 20px;
}

.genie-lamp {
    font-size: 4rem;
    display: inline-block;
    animation: lampGlow 2s ease-in-out infinite alternate;
    filter: drop-shadow(0 0 15px #ffd700);
}

@keyframes lampGlow {
    from { filter: drop-shadow(0 0 10px #ffd700); }
    to { filter: drop-shadow(0 0 20px #ffd700); }
}

.genie-smoke {
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 40px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: smokeRise 3s ease-in-out infinite;
}

@keyframes smokeRise {
    0%, 100% { opacity: 0.3; transform: translateX(-50%) translateY(0) scale(1); }
    50% { opacity: 0.7; transform: translateX(-50%) translateY(-20px) scale(1.2); }
}

.genie-title {
    font-size: 2.5rem;
    color: #ffd700;
    margin: 0 0 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    font-weight: bold;
}

.genie-subtitle {
    font-size: 1.2rem;
    color: #e6e6fa;
    margin: 0 0 20px;
    font-style: italic;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.token-display {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    background: rgba(0, 255, 255, 0.1);
    border: 2px solid #00ffff;
    border-radius: 25px;
    padding: 10px 20px;
    font-size: 1.3rem;
    color: #00ffff;
    text-shadow: 0 0 10px #00ffff;
}

.token-icon {
    font-size: 1.5rem;
    animation: tokenSparkle 1.5s ease-in-out infinite;
}

@keyframes tokenSparkle {
    0%, 100% { transform: scale(1); filter: drop-shadow(0 0 5px #00ffff); }
    50% { transform: scale(1.1); filter: drop-shadow(0 0 10px #00ffff); }
}

.token-amount {
    font-weight: bold;
    font-size: 1.4rem;
}

.token-label {
    font-size: 1rem;
    opacity: 0.9;
}

/* Body */
.genie-body {
    padding: 20px;
}

.section-title {
    font-size: 1.8rem;
    color: #ffd700;
    margin: 0 0 20px;
    text-align: center;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
}

/* Power-ups Grid */
.power-ups-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.power-up-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.power-up-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 215, 0, 0.6);
    box-shadow: 0 10px 20px rgba(255, 215, 0, 0.2);
}

.power-up-card.active {
    border-color: #00ff88;
    background: linear-gradient(135deg, rgba(0, 255, 136, 0.2), rgba(0, 255, 136, 0.1));
}

.power-up-card.unavailable {
    opacity: 0.6;
    border-color: rgba(255, 255, 255, 0.2);
}

.power-up-card.unavailable:hover {
    transform: none;
    box-shadow: none;
}

.power-up-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.power-up-name {
    font-size: 1.4rem;
    color: #ffd700;
    margin: 0 0 10px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.power-up-description {
    font-size: 0.95rem;
    color: #e6e6fa;
    margin: 0 0 15px;
    line-height: 1.4;
}

.power-up-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.power-up-cost {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #00ffff;
    font-weight: bold;
}

.power-up-duration {
    color: #ffd700;
    font-weight: bold;
}

.power-up-status {
    font-size: 0.85rem;
    color: #ff6b6b;
    margin-bottom: 15px;
    min-height: 20px;
}

.power-up-card.active .power-up-status {
    color: #00ff88;
}

.power-up-button {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.power-up-button.primary {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a0537;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.power-up-button.primary:hover {
    background: linear-gradient(135deg, #ffed4e, #ffd700);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
    transform: translateY(-2px);
}

.power-up-button.disabled {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.5);
    cursor: not-allowed;
    box-shadow: none;
}

/* Reality Warp Section */
.reality-warp-section {
    margin-bottom: 30px;
}

.warp-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 20px;
}

.warp-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.warp-card:hover {
    transform: translateY(-5px);
    border-color: rgba(255, 215, 0, 0.6);
    box-shadow: 0 10px 20px rgba(255, 215, 0, 0.2);
}

.warp-card.active {
    border-color: #00ff88;
    background: linear-gradient(135deg, rgba(0, 255, 136, 0.2), rgba(0, 255, 136, 0.1));
}

.warp-card.unavailable {
    opacity: 0.6;
    border-color: rgba(255, 255, 255, 0.2);
}

.warp-card.unavailable:hover {
    transform: none;
    box-shadow: none;
}

.warp-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    display: block;
}

.warp-name {
    font-size: 1.4rem;
    color: #ffd700;
    margin: 0 0 10px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.warp-description {
    font-size: 0.95rem;
    color: #e6e6fa;
    margin: 0 0 15px;
    line-height: 1.4;
}

.warp-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    font-size: 0.9rem;
}

.warp-cost {
    display: flex;
    align-items: center;
    gap: 5px;
    color: #00ffff;
    font-weight: bold;
}

.warp-tier {
    color: #ffd700;
    font-weight: bold;
    text-transform: uppercase;
    font-size: 0.8rem;
    padding: 4px 8px;
    background: rgba(255, 215, 0, 0.2);
    border-radius: 4px;
}

.warp-status {
    font-size: 0.85rem;
    color: #ff6b6b;
    margin-bottom: 15px;
    min-height: 20px;
}

.warp-card.active .warp-status {
    color: #00ff88;
}

.warp-button {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.warp-button.primary {
    background: linear-gradient(135deg, #ffd700, #ffed4e);
    color: #1a0537;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

.warp-button.primary:hover {
    background: linear-gradient(135deg, #ffed4e, #ffd700);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
    transform: translateY(-2px);
}

.warp-button.disabled {
    background: rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.5);
    cursor: not-allowed;
    box-shadow: none;
}

/* Footer */
.genie-footer {
    text-align: center;
    padding: 20px;
    border-top: 2px solid rgba(255, 215, 0, 0.3);
}

.genie-button {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.genie-button.secondary {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
    color: #e6e6fa;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.genie-button.secondary:hover {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.3), rgba(255, 255, 255, 0.2));
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(255, 255, 255, 0.2);
}

/* Feedback Messages */
.genie-feedback {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10;
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-width: 300px;
}

.feedback-message {
    padding: 12px 16px;
    border-radius: 8px;
    color: white;
    font-size: 14px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(5px);
    animation: feedbackSlideIn 0.3s ease-out;
}

.feedback-success {
    background: rgba(0, 255, 136, 0.9);
    border: 2px solid #00ff88;
}

.feedback-error {
    background: rgba(255, 68, 68, 0.9);
    border: 2px solid #ff4444;
}

.feedback-warning {
    background: rgba(255, 170, 0, 0.9);
    border: 2px solid #ffaa00;
}

.feedback-info {
    background: rgba(0, 255, 255, 0.9);
    border: 2px solid #00ffff;
}

@keyframes feedbackSlideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .genie-content {
        width: 95%;
        max-height: 95vh;
    }

    .genie-title {
        font-size: 2rem;
    }

    .power-ups-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .power-up-card {
        padding: 15px;
    }

    .token-display {
        font-size: 1.1rem;
        padding: 8px 16px;
    }

    .genie-feedback {
        right: 10px;
        top: 10px;
        max-width: 250px;
    }

    .feedback-message {
        font-size: 12px;
        padding: 10px 12px;
    }
}
