/**
 * Game configuration constants
 */
export const GAME_CONFIG = {
    // Canvas settings
    CANVAS_WIDTH: 800,
    CANVAS_HEIGHT: 600,
    TARGET_FPS: 60,
    
    // Game mechanics
    PLAYER_LIVES: 3,
    PLAYER_HEALTH: 100,
    
    // Token economy
    BASE_LEVEL_REWARD: 50,
    POWER_UP_COSTS: {
        EXTRA_WINGMAN: 100,
        EXTRA_LIFE: 150,
        SPREAD_AMMO: 75,
        REALITY_WARP: 250
    },
    
    // Reality warp costs
    WARP_BASE_COST: 200,
    
    // Raffle system
    RAFFLE_PRIZE_POOL_PERCENTAGE: 0.5,
    RAFFLE_PRIZES: {
        GOLD: 250,
        SILVER: 150,
        BRONZE: 100
    },
    
    // Development settings
    DEBUG_MODE: true,
    ENABLE_CONSOLE_LOGS: true
};

export const ENVIRONMENT_TYPES = {
    SPACE: 'space',
    UNDERWATER: 'underwater',
    VOLCANIC: 'volcanic',
    CRYSTAL: 'crystal',
    FOREST: 'forest',
    DESERT: 'desert',
    ICE: 'ice'
};

export const ENEMY_TYPES = {
    WATER: 'water',
    FIRE: 'fire',
    AIR: 'air',
    EARTH: 'earth',
    CRYSTAL: 'crystal',
    SHADOW: 'shadow'
};