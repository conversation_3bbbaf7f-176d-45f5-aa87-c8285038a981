import { GAME_CONFIG } from '../config/gameConfig.js';

/**
 * LeaderboardManager handles participant registration, scoring, and leaderboard period management
 * Manages daily/weekly contest cycles and raffle qualification logic
 */
export class LeaderboardManager {
    constructor(tokenEconomyManager = null) {
        // Token economy integration
        this.tokenEconomyManager = tokenEconomyManager;
        
        // Contest periods
        this.CONTEST_PERIODS = {
            DAILY: 'daily',
            WEEKLY: 'weekly'
        };
        
        // Prize tiers (Requirement 8.2)
        this.PRIZE_TIERS = {
            GOLD: { name: 'Gold', tokens: 250, position: 1 },
            SILVER: { name: 'Silver', tokens: 150, position: 2 },
            BRONZE: { name: 'Bronze', tokens: 100, position: 3 }
        };
        
        // Contest period settings
        this.contestPeriodSettings = {
            daily: {
                duration: 24 * 60 * 60 * 1000, // 24 hours in milliseconds
                startTime: this.getStartOfPeriod('daily'),
                endTime: this.getEndOfPeriod('daily'),
                raffleQualificationScore: 1000, // Minimum score to qualify for daily raffle
                prizePoolPercentage: 0.5, // 50% of net token profits (Requirement 8.3)
                raffleWinners: 5 // Number of raffle winners to select
            },
            weekly: {
                duration: 7 * 24 * 60 * 60 * 1000, // 7 days in milliseconds
                startTime: this.getStartOfPeriod('weekly'),
                endTime: this.getEndOfPeriod('weekly'),
                raffleQualificationScore: 5000, // Minimum score to qualify for weekly raffle
                prizePoolPercentage: 0.5, // 50% of net token profits (Requirement 8.3)
                raffleWinners: 10 // Number of raffle winners to select
            }
        };
        
        // Current contest period
        this.currentContestPeriod = this.CONTEST_PERIODS.DAILY;
        
        // Participant tracking
        this.participants = new Map(); // Map of player IDs to participant data
        this.participantPerformance = new Map(); // Map of player IDs to performance metrics
        
        // Leaderboard data
        this.leaderboards = {
            daily: [],
            weekly: []
        };
        
        // Contest winners
        this.contestWinners = {
            daily: [],
            weekly: []
        };
        
        // Raffle qualification tracking
        this.raffleQualifiedPlayers = {
            daily: new Set(),
            weekly: new Set()
        };
        
        // Prize distribution tracking
        this.prizePools = {
            daily: 0,
            weekly: 0
        };
        
        // Raffle winners tracking
        this.raffleWinners = {
            daily: [],
            weekly: []
        };
        
        // Prize distribution history
        this.prizeDistributionHistory = [];
        
        // Callbacks
        this.onLeaderboardUpdateCallback = null;
        this.onContestEndCallback = null;
        this.onRaffleQualificationCallback = null;
        this.onPrizeDistributedCallback = null;
        this.onRaffleWinnerSelectedCallback = null;
        
        // Debug mode
        this.debugMode = GAME_CONFIG.DEBUG_MODE;
        
    }
    
    /**
     * Get start time of contest period
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {number} Start timestamp
     */
    getStartOfPeriod(period) {
        const now = new Date();
        
        if (period === 'daily') {
            // Start of day (midnight)
            const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            return startOfDay.getTime();
        } else if (period === 'weekly') {
            // Start of week (Sunday)
            const dayOfWeek = now.getDay(); // 0 = Sunday
            const startOfWeek = new Date(now);
            startOfWeek.setDate(now.getDate() - dayOfWeek);
            startOfWeek.setHours(0, 0, 0, 0);
            return startOfWeek.getTime();
        }
        
        return now.getTime();
    }
    
    /**
     * Get end time of contest period
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {number} End timestamp
     */
    getEndOfPeriod(period) {
        const startTime = this.getStartOfPeriod(period);
        const duration = this.contestPeriodSettings[period].duration;
        return startTime + duration;
    }
    
    /**
     * Check if a contest period has ended
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {boolean} True if contest period has ended
     */
    hasContestPeriodEnded(period) {
        const now = Date.now();
        return now >= this.contestPeriodSettings[period].endTime;
    }
    
    /**
     * Get time remaining in current contest period
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {number} Time remaining in milliseconds
     */
    getTimeRemaining(period) {
        const now = Date.now();
        const endTime = this.contestPeriodSettings[period].endTime;
        return Math.max(0, endTime - now);
    }
    
    /**
     * Update contest periods and check for period transitions
     */
    updateContestPeriods() {
        // Check daily contest
        if (this.hasContestPeriodEnded('daily')) {
            this.endContestPeriod('daily');
            this.startNewContestPeriod('daily');
        }
        
        // Check weekly contest
        if (this.hasContestPeriodEnded('weekly')) {
            this.endContestPeriod('weekly');
            this.startNewContestPeriod('weekly');
        }
    }
    
    /**
     * End a contest period and calculate winners
     * @param {string} period - Contest period ('daily' or 'weekly')
     */
    endContestPeriod(period) {
        
        try {
            // Calculate top performers based on completion scores (Requirement 8.1)
            const leaderboard = this.calculateLeaderboard(period);
            this.leaderboards[period] = leaderboard;
            
            // Calculate prize pool (Requirement 8.3)
            const prizePool = this.calculatePrizePool(period);
            
            // Select winners for Gold, Silver, and Bronze tiers (Requirement 8.2)
            const winners = this.selectWinners(period);
            this.contestWinners[period] = [
                winners.gold,
                winners.silver,
                winners.bronze
            ].filter(winner => winner !== null);
            
            // Distribute prizes to winners (Requirement 8.2)
            const prizeDistribution = this.distributePrizes(period, winners);
            
            // Check raffle qualifications
            this.updateRaffleQualifications(period);
            
            // Select and reward raffle winners (Requirement 8.5)
            const raffleWinners = this.selectRaffleWinners(period);
            if (raffleWinners.length > 0) {
                const raffleDistribution = this.distributeRafflePrizes(period, raffleWinners);
            }
            
            // Trigger contest end callback
            if (this.onContestEndCallback) {
                this.onContestEndCallback(period, this.contestWinners[period]);
            }
            
        } catch (error) {
            
            // Trigger contest end callback with error
            if (this.onContestEndCallback) {
                this.onContestEndCallback(period, [], error);
            }
        }
    }
    
    /**
     * Start a new contest period
     * @param {string} period - Contest period ('daily' or 'weekly')
     */
    startNewContestPeriod(period) {
        // Update period times
        this.contestPeriodSettings[period].startTime = this.getStartOfPeriod(period);
        this.contestPeriodSettings[period].endTime = this.getEndOfPeriod(period);
        
        // Clear raffle qualifications for new period
        this.raffleQualifiedPlayers[period].clear();
        
    }
    
    /**
     * Register a player for leaderboard contests
     * @param {string} playerId - Player ID
     * @param {string} playerName - Player name
     * @param {object} playerData - Additional player data
     * @returns {object} Registration result
     */
    registerPlayer(playerId, playerName, playerData = {}) {
        if (this.participants.has(playerId)) {
            return { success: false, reason: 'already_registered' };
        }
        
        // Create participant data
        const participant = {
            id: playerId,
            name: playerName,
            registrationDate: Date.now(),
            totalScore: 0,
            gamesPlayed: 0,
            bestScore: 0,
            averageCompletionTime: 0,
            enemiesDefeated: 0,
            levelsCompleted: 0,
            ...playerData
        };
        
        // Store participant
        this.participants.set(playerId, participant);
        
        // Initialize performance tracking
        this.participantPerformance.set(playerId, {
            daily: {
                scores: [],
                totalScore: 0,
                gamesPlayed: 0,
                bestScore: 0,
                completionTimes: [],
                enemiesDefeated: 0,
                levelsCompleted: 0
            },
            weekly: {
                scores: [],
                totalScore: 0,
                gamesPlayed: 0,
                bestScore: 0,
                completionTimes: [],
                enemiesDefeated: 0,
                levelsCompleted: 0
            }
        });
        
        
        return { success: true, participant };
    }
    
    /**
     * Unregister a player from leaderboard contests
     * @param {string} playerId - Player ID
     * @returns {object} Unregistration result
     */
    unregisterPlayer(playerId) {
        if (!this.participants.has(playerId)) {
            return { success: false, reason: 'not_registered' };
        }
        
        // Remove participant
        this.participants.delete(playerId);
        
        // Remove performance tracking
        this.participantPerformance.delete(playerId);
        
        // Remove from raffle qualifications
        this.raffleQualifiedPlayers.daily.delete(playerId);
        this.raffleQualifiedPlayers.weekly.delete(playerId);
        
        
        return { success: true };
    }
    
    /**
     * Record game performance for a player
     * @param {string} playerId - Player ID
     * @param {object} gameData - Game performance data
     * @returns {object} Result of recording performance
     */
    recordGamePerformance(playerId, gameData) {
        if (!this.participants.has(playerId)) {
            return { success: false, reason: 'not_registered' };
        }
        
        // Extract game metrics
        const {
            score = 0,
            completionTime = 0,
            enemiesDefeated = 0,
            levelsCompleted = 0,
            gameSessionId = this.generateSessionId()
        } = gameData;
        
        // Calculate completion score based on game metrics
        const completionScore = this.calculateCompletionScore(gameData);
        
        // Update participant data
        const participant = this.participants.get(playerId);
        participant.totalScore += completionScore;
        participant.gamesPlayed++;
        participant.bestScore = Math.max(participant.bestScore, completionScore);
        participant.averageCompletionTime = this.calculateAverageCompletionTime(
            participant.averageCompletionTime, 
            completionTime, 
            participant.gamesPlayed
        );
        participant.enemiesDefeated += enemiesDefeated;
        participant.levelsCompleted += levelsCompleted;
        
        // Update performance tracking for both daily and weekly
        const performance = this.participantPerformance.get(playerId);
        
        // Daily performance
        performance.daily.scores.push({
            score: completionScore,
            timestamp: Date.now(),
            gameSessionId
        });
        performance.daily.totalScore += completionScore;
        performance.daily.gamesPlayed++;
        performance.daily.bestScore = Math.max(performance.daily.bestScore, completionScore);
        performance.daily.completionTimes.push(completionTime);
        performance.daily.enemiesDefeated += enemiesDefeated;
        performance.daily.levelsCompleted += levelsCompleted;
        
        // Weekly performance
        performance.weekly.scores.push({
            score: completionScore,
            timestamp: Date.now(),
            gameSessionId
        });
        performance.weekly.totalScore += completionScore;
        performance.weekly.gamesPlayed++;
        performance.weekly.bestScore = Math.max(performance.weekly.bestScore, completionScore);
        performance.weekly.completionTimes.push(completionTime);
        performance.weekly.enemiesDefeated += enemiesDefeated;
        performance.weekly.levelsCompleted += levelsCompleted;
        
        // Check for raffle qualification (Requirement 8.4)
        this.checkRaffleQualification(playerId, 'daily');
        this.checkRaffleQualification(playerId, 'weekly');
        
        // Update leaderboards
        this.updateLeaderboards();
        
        
        return { 
            success: true, 
            completionScore,
            participant
        };
    }
    
    /**
     * Calculate completion score based on game metrics
     * @param {object} gameData - Game performance data
     * @returns {number} Completion score
     */
    calculateCompletionScore(gameData) {
        const {
            score = 0,
            completionTime = 0,
            enemiesDefeated = 0,
            levelsCompleted = 0,
            bonuses = {}
        } = gameData;
        
        // Base score from points earned
        let completionScore = score;
        
        // Time bonus (faster completion = higher score)
        if (completionTime > 0) {
            const timeBonus = Math.max(0, 1000 - completionTime); // Bonus for completing under 1000 seconds
            completionScore += timeBonus;
        }
        
        // Enemy defeated bonus
        const enemyBonus = enemiesDefeated * 10;
        completionScore += enemyBonus;
        
        // Level completion bonus
        const levelBonus = levelsCompleted * 100;
        completionScore += levelBonus;
        
        // Performance bonuses
        if (bonuses.speed) completionScore += 200;
        if (bonuses.accuracy) completionScore += 150;
        if (bonuses.perfect) completionScore += 500;
        
        return Math.floor(completionScore);
    }
    
    /**
     * Calculate average completion time
     * @param {number} currentAverage - Current average time
     * @param {number} newTime - New completion time
     * @param {number} gameCount - Number of games played
     * @returns {number} New average time
     */
    calculateAverageCompletionTime(currentAverage, newTime, gameCount) {
        if (gameCount <= 1) return newTime;
        
        return ((currentAverage * (gameCount - 1)) + newTime) / gameCount;
    }
    
    /**
     * Calculate leaderboard for a contest period
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {Array} Sorted leaderboard
     */
    calculateLeaderboard(period) {
        const leaderboard = [];
        
        // Process all participants
        for (const [playerId, participant] of this.participants) {
            const performance = this.participantPerformance.get(playerId);
            if (!performance || !performance[period]) continue;
            
            const periodPerformance = performance[period];
            
            // Only include players who have played in this period
            if (periodPerformance.gamesPlayed === 0) continue;
            
            leaderboard.push({
                playerId,
                playerName: participant.name,
                totalScore: periodPerformance.totalScore,
                bestScore: periodPerformance.bestScore,
                gamesPlayed: periodPerformance.gamesPlayed,
                averageCompletionTime: periodPerformance.completionTimes.length > 0 
                    ? periodPerformance.completionTimes.reduce((a, b) => a + b, 0) / periodPerformance.completionTimes.length 
                    : 0,
                enemiesDefeated: periodPerformance.enemiesDefeated,
                levelsCompleted: periodPerformance.levelsCompleted
            });
        }
        
        // Sort by total score (descending)
        leaderboard.sort((a, b) => b.totalScore - a.totalScore);
        
        return leaderboard;
    }
    
    /**
     * Update leaderboards for all contest periods
     */
    updateLeaderboards() {
        // Update contest periods first
        this.updateContestPeriods();
        
        // Calculate leaderboards
        this.leaderboards.daily = this.calculateLeaderboard('daily');
        this.leaderboards.weekly = this.calculateLeaderboard('weekly');
        
        // Trigger leaderboard update callback
        if (this.onLeaderboardUpdateCallback) {
            this.onLeaderboardUpdateCallback({
                daily: this.leaderboards.daily,
                weekly: this.leaderboards.weekly
            });
        }
    }
    
    /**
     * Get leaderboard for a contest period
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @param {number} limit - Maximum number of entries to return
     * @returns {Array} Leaderboard entries
     */
    getLeaderboard(period, limit = 10) {
        if (!this.leaderboards[period]) {
            this.leaderboards[period] = this.calculateLeaderboard(period);
        }
        
        return this.leaderboards[period].slice(0, limit);
    }
    
    /**
     * Get player ranking in a contest period
     * @param {string} playerId - Player ID
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {object} Player ranking data
     */
    getPlayerRanking(playerId, period) {
        if (!this.participants.has(playerId)) {
            return { success: false, reason: 'not_registered' };
        }
        
        const leaderboard = this.getLeaderboard(period, 0); // Get full leaderboard
        const playerEntry = leaderboard.find(entry => entry.playerId === playerId);
        
        if (!playerEntry) {
            return { 
                success: true, 
                ranked: false,
                message: 'Player has no scores in this period'
            };
        }
        
        const rank = leaderboard.indexOf(playerEntry) + 1;
        const totalPlayers = leaderboard.length;
        
        return {
            success: true,
            ranked: true,
            rank,
            totalPlayers,
            percentile: Math.floor((1 - rank / totalPlayers) * 100),
            playerData: playerEntry
        };
    }
    
    /**
     * Check if a player qualifies for raffle
     * @param {string} playerId - Player ID
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {boolean} True if player qualifies
     */
    checkRaffleQualification(playerId, period) {
        if (!this.participants.has(playerId)) {
            return false;
        }
        
        const performance = this.participantPerformance.get(playerId);
        if (!performance || !performance[period]) {
            return false;
        }
        
        const periodPerformance = performance[period];
        const qualificationScore = this.contestPeriodSettings[period].raffleQualificationScore;
        
        // Check if player meets qualification score
        const qualifies = periodPerformance.totalScore >= qualificationScore;
        
        // Update raffle qualification tracking
        if (qualifies && !this.raffleQualifiedPlayers[period].has(playerId)) {
            this.raffleQualifiedPlayers[period].add(playerId);
            
            // Notify player of raffle eligibility (Requirement 8.4)
            if (this.onRaffleQualificationCallback) {
                const participant = this.participants.get(playerId);
                this.onRaffleQualificationCallback(playerId, participant.name, period);
            }
            
            console.log(`Player ${playerId} qualified for ${period} raffle with score ${periodPerformance.totalScore}`);
        }
        
        return qualifies;
    }
    
    /**
     * Update raffle qualifications for all players in a contest period
     * @param {string} period - Contest period ('daily' or 'weekly')
     */
    updateRaffleQualifications(period) {
        for (const playerId of this.participants.keys()) {
            this.checkRaffleQualification(playerId, period);
        }
    }
    
    /**
     * Get players qualified for raffle
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {Array} Array of qualified player data
     */
    getRaffleQualifiedPlayers(period) {
        const qualifiedPlayers = [];
        
        for (const playerId of this.raffleQualifiedPlayers[period]) {
            const participant = this.participants.get(playerId);
            if (participant) {
                qualifiedPlayers.push({
                    playerId,
                    playerName: participant.name,
                    totalScore: this.participantPerformance.get(playerId)[period].totalScore
                });
            }
        }
        
        // Sort by score (descending)
        qualifiedPlayers.sort((a, b) => b.totalScore - a.totalScore);
        
        return qualifiedPlayers;
    }
    
    /**
     * Get contest winners for a period
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {Array} Array of winner data
     */
    getContestWinners(period) {
        return this.contestWinners[period] || [];
    }
    
    /**
     * Get contest period status
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {object} Contest period status
     */
    getContestPeriodStatus(period) {
        const now = Date.now();
        const settings = this.contestPeriodSettings[period];
        
        return {
            period,
            startTime: settings.startTime,
            endTime: settings.endTime,
            timeRemaining: this.getTimeRemaining(period),
            progress: Math.min(1, (now - settings.startTime) / settings.duration),
            isActive: now >= settings.startTime && now < settings.endTime
        };
    }
    
    /**
     * Get player statistics
     * @param {string} playerId - Player ID
     * @returns {object} Player statistics
     */
    getPlayerStatistics(playerId) {
        if (!this.participants.has(playerId)) {
            return { success: false, reason: 'not_registered' };
        }
        
        const participant = this.participants.get(playerId);
        const performance = this.participantPerformance.get(playerId);
        
        return {
            success: true,
            player: participant,
            performance: {
                daily: performance.daily,
                weekly: performance.weekly
            },
            rankings: {
                daily: this.getPlayerRanking(playerId, 'daily'),
                weekly: this.getPlayerRanking(playerId, 'weekly')
            },
            raffleQualified: {
                daily: this.raffleQualifiedPlayers.daily.has(playerId),
                weekly: this.raffleQualifiedPlayers.weekly.has(playerId)
            }
        };
    }
    
    /**
     * Get leaderboard statistics
     * @returns {object} Leaderboard statistics
     */
    getLeaderboardStatistics() {
        return {
            totalParticipants: this.participants.size,
            contestPeriods: {
                daily: this.getContestPeriodStatus('daily'),
                weekly: this.getContestPeriodStatus('weekly')
            },
            raffleQualified: {
                daily: this.raffleQualifiedPlayers.daily.size,
                weekly: this.raffleQualifiedPlayers.weekly.size
            },
            winners: {
                daily: this.contestWinners.daily,
                weekly: this.contestWinners.weekly
            }
        };
    }
    
    /**
     * Set callback for leaderboard updates
     * @param {function} callback - Callback function
     */
    setLeaderboardUpdateCallback(callback) {
        this.onLeaderboardUpdateCallback = callback;
    }
    
    /**
     * Set callback for contest end
     * @param {function} callback - Callback function
     */
    setContestEndCallback(callback) {
        this.onContestEndCallback = callback;
    }
    
    /**
     * Set callback for raffle qualification
     * @param {function} callback - Callback function
     */
    setRaffleQualificationCallback(callback) {
        this.onRaffleQualificationCallback = callback;
    }
    
    /**
     * Set token economy manager
     * @param {TokenEconomyManager} tokenEconomyManager - Token economy manager instance
     */
    setTokenEconomyManager(tokenEconomyManager) {
        this.tokenEconomyManager = tokenEconomyManager;
        console.log('TokenEconomyManager set in LeaderboardManager');
    }
    
    /**
     * Generate unique session ID
     * @returns {string} Session ID
     */
    generateSessionId() {
        return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Reset leaderboard data (for testing or new season)
     * @param {string} period - Contest period to reset ('daily', 'weekly', or 'all')
     */
    resetLeaderboard(period = 'all') {
        if (period === 'all' || period === 'daily') {
            this.leaderboards.daily = [];
            this.contestWinners.daily = [];
            this.raffleQualifiedPlayers.daily.clear();
            this.raffleWinners.daily = [];
            this.prizePools.daily = 0;
            
            // Reset daily performance for all players
            for (const performance of this.participantPerformance.values()) {
                performance.daily = {
                    scores: [],
                    totalScore: 0,
                    gamesPlayed: 0,
                    bestScore: 0,
                    completionTimes: [],
                    enemiesDefeated: 0,
                    levelsCompleted: 0
                };
            }
        }
        
        if (period === 'all' || period === 'weekly') {
            this.leaderboards.weekly = [];
            this.contestWinners.weekly = [];
            this.raffleQualifiedPlayers.weekly.clear();
            this.raffleWinners.weekly = [];
            this.prizePools.weekly = 0;
            
            // Reset weekly performance for all players
            for (const performance of this.participantPerformance.values()) {
                performance.weekly = {
                    scores: [],
                    totalScore: 0,
                    gamesPlayed: 0,
                    bestScore: 0,
                    completionTimes: [],
                    enemiesDefeated: 0,
                    levelsCompleted: 0
                };
            }
        }
        
        console.log(`Leaderboard reset for period: ${period}`);
    }
    
    /**
     * Calculate prize pool for a contest period (Requirement 8.3)
     * @param {string} period - Contest period ('daily' or 'weekly')
     * @returns {number} Calculated prize pool amount
     */
    calculatePrizePool(period) {
                if (!this.tokenEconomyManager) {
                    console.error('TokenEconomyManager not set. Cannot calculate prize pool.');
                    return 0;
                }
                
                // Get token economy statistics
                const stats = this.tokenEconomyManager.getStatistics();
                const netTokenProfits = stats.totalEarned - stats.totalSpent;
                
                // Calculate prize pool as 50% of net token profits
                const prizePoolPercentage = this.contestPeriodSettings[period].prizePoolPercentage;
                const prizePool = Math.floor(netTokenProfits * prizePoolPercentage);
                
                // Store prize pool for distribution
                this.prizePools[period] = prizePool;
                
                console.log(`Calculated ${period} prize pool: ${prizePool} WISH tokens (${prizePoolPercentage * 100}% of ${netTokenProfits} net profits)`);
                
                return prizePool;
            }
            
            /**
             * Select winners for Gold, Silver, and Bronze tiers (Requirement 8.2)
             * @param {string} period - Contest period ('daily' or 'weekly')
             * @returns {object} Winners by tier
             */
            selectWinners(period) {
                const leaderboard = this.getLeaderboard(period, 0); // Get full leaderboard
                
                if (leaderboard.length === 0) {
                    console.warn(`No participants in ${period} leaderboard. No winners selected.`);
                    return {
                        gold: null,
                        silver: null,
                        bronze: null
                    };
                }
                
                // Select top 3 performers with tie-breaking
                const winners = {
                    gold: this.selectWinnerByPosition(leaderboard, 1, 'Gold'),
                    silver: this.selectWinnerByPosition(leaderboard, 2, 'Silver'),
                    bronze: this.selectWinnerByPosition(leaderboard, 3, 'Bronze')
                };
                
                console.log(`Selected ${period} winners:`, winners);
                
                return winners;
            }
            
            /**
             * Select winner for a specific position with tie-breaking
             * @param {Array} leaderboard - Sorted leaderboard
             * @param {number} position - Position to select (1, 2, or 3)
             * @param {string} tier - Prize tier name
             * @returns {object|null} Winner data or null if no winner
             */
            selectWinnerByPosition(leaderboard, position, tier) {
                if (leaderboard.length < position) {
                    return null;
                }
                
                // Get the base winner
                let winner = leaderboard[position - 1];
                const targetScore = winner.totalScore;
                
                // Check for ties at this position
                const tiedPlayers = [winner];
                for (let i = position; i < leaderboard.length; i++) {
                    if (leaderboard[i].totalScore === targetScore) {
                        tiedPlayers.push(leaderboard[i]);
                    } else {
                        break; // No more ties
                    }
                }
                
                // If there's a tie, use tie-breaking criteria
                if (tiedPlayers.length > 1) {
                    winner = this.breakTie(tiedPlayers);
                    console.log(`Tie broken for ${tier} position among ${tiedPlayers.length} players. Winner: ${winner.playerName}`);
                }
                
                return {
                    ...winner,
                    position: position,
                    tier: tier.toLowerCase(),
                    prizeTokens: this.PRIZE_TIERS[tier.toUpperCase()].tokens
                };
            }
            
            /**
             * Break ties between players with the same score
             * @param {Array} tiedPlayers - Players with the same score
             * @returns {object} Winning player
             */
            breakTie(tiedPlayers) {
                // Tie-breaking criteria:
                // 1. Higher best score
                // 2. More games played
                // 3. Lower average completion time
                // 4. Random selection if still tied
                
                // Sort by best score (descending)
                tiedPlayers.sort((a, b) => b.bestScore - a.bestScore);
                
                if (tiedPlayers[0].bestScore !== tiedPlayers[1].bestScore) {
                    return tiedPlayers[0];
                }
                
                // Filter to players with the same best score
                const sameBestScore = tiedPlayers.filter(p => p.bestScore === tiedPlayers[0].bestScore);
                
                // Sort by games played (descending)
                sameBestScore.sort((a, b) => b.gamesPlayed - a.gamesPlayed);
                
                if (sameBestScore[0].gamesPlayed !== sameBestScore[1].gamesPlayed) {
                    return sameBestScore[0];
                }
                
                // Filter to players with the same games played
                const sameGamesPlayed = sameBestScore.filter(p => p.gamesPlayed === sameBestScore[0].gamesPlayed);
                
                // Sort by average completion time (ascending)
                sameGamesPlayed.sort((a, b) => a.averageCompletionTime - b.averageCompletionTime);
                
                if (sameGamesPlayed[0].averageCompletionTime !== sameGamesPlayed[1].averageCompletionTime) {
                    return sameGamesPlayed[0];
                }
                
                // Random selection for players still tied
                const stillTied = sameGamesPlayed.filter(p => p.averageCompletionTime === sameGamesPlayed[0].averageCompletionTime);
                return stillTied[Math.floor(Math.random() * stillTied.length)];
            }
            
            /**
             * Distribute prizes to contest winners (Requirement 8.2)
             * @param {string} period - Contest period ('daily' or 'weekly')
             * @param {object} winners - Winners by tier
             * @returns {object} Distribution results
             */
            distributePrizes(period, winners) {
                if (!this.tokenEconomyManager) {
                    console.error('TokenEconomyManager not set. Cannot distribute prizes.');
                    return { success: false, reason: 'token_manager_not_set' };
                }
                
                const distributionResults = {
                    period,
                    timestamp: Date.now(),
                    winners: {},
                    totalDistributed: 0
                };
                
                // Distribute prizes to each tier winner
                for (const [tierKey, tierInfo] of Object.entries(this.PRIZE_TIERS)) {
                    const tier = tierKey.toLowerCase();
                    const winner = winners[tier];
                    
                    if (winner) {
                        // Award tokens to winner
                        const result = this.tokenEconomyManager.awardTokens(
                            tierInfo.tokens,
                            `${period}_${tier}_prize`,
                            {
                                playerId: winner.playerId,
                                playerName: winner.playerName,
                                period: period,
                                tier: tier,
                                position: tierInfo.position,
                                score: winner.totalScore
                            }
                        );
                        
                        if (result.success) {
                            distributionResults.winners[tier] = {
                                playerId: winner.playerId,
                                playerName: winner.playerName,
                                position: tierInfo.position,
                                prizeTokens: tierInfo.tokens,
                                transactionId: result.transaction.id
                            };
                            
                            distributionResults.totalDistributed += tierInfo.tokens;
                            
                            // Notify winner (Requirement 8.5)
                            this.notifyWinner(winner, period, tier, tierInfo.tokens);
                            
                            console.log(`Distributed ${tierInfo.tokens} WISH tokens to ${period} ${tier} winner: ${winner.playerName}`);
                        } else {
                            console.error(`Failed to distribute ${tier} prize to ${winner.playerName}:`, result.reason);
                            distributionResults.winners[tier] = {
                                playerId: winner.playerId,
                                playerName: winner.playerName,
                                position: tierInfo.position,
                                prizeTokens: tierInfo.tokens,
                                error: result.reason
                            };
                        }
                    } else {
                        console.log(`No ${tier} winner for ${period} contest`);
                        distributionResults.winners[tier] = null;
                    }
                }
                
                // Record distribution in history
                this.prizeDistributionHistory.push(distributionResults);
                
                // Trigger callback
                if (this.onPrizeDistributedCallback) {
                    this.onPrizeDistributedCallback(distributionResults);
                }
                
                return distributionResults;
            }
            
            /**
             * Select raffle winners from qualified participants
             * @param {string} period - Contest period ('daily' or 'weekly')
             * @returns {Array} Selected raffle winners
             */
            selectRaffleWinners(period) {
                const qualifiedPlayers = this.getRaffleQualifiedPlayers(period);
                const maxWinners = this.contestPeriodSettings[period].raffleWinners;
                
                if (qualifiedPlayers.length === 0) {
                    console.log(`No qualified players for ${period} raffle`);
                    this.raffleWinners[period] = [];
                    return [];
                }
                
                // Calculate raffle prize amount (equal share of remaining prize pool after tier prizes)
                const tierPrizesTotal = Object.values(this.PRIZE_TIERS).reduce((sum, tier) => sum + tier.tokens, 0);
                const remainingPrizePool = Math.max(0, this.prizePools[period] - tierPrizesTotal);
                const rafflePrizePerWinner = qualifiedPlayers.length > 0 ? Math.floor(remainingPrizePool / Math.min(maxWinners, qualifiedPlayers.length)) : 0;
                
                // Randomly select winners
                const shuffled = [...qualifiedPlayers].sort(() => 0.5 - Math.random());
                const selectedWinners = shuffled.slice(0, Math.min(maxWinners, qualifiedPlayers.length));
                
                // Process each winner
                const processedWinners = selectedWinners.map((winner, index) => {
                    return {
                        ...winner,
                        rafflePosition: index + 1,
                        prizeTokens: rafflePrizePerWinner
                    };
                });
                
                this.raffleWinners[period] = processedWinners;
                
                console.log(`Selected ${processedWinners.length} ${period} raffle winners from ${qualifiedPlayers.length} qualified players`);
                
                return processedWinners;
            }
            
            /**
             * Distribute raffle prizes to winners (Requirement 8.5)
             * @param {string} period - Contest period ('daily' or 'weekly')
             * @param {Array} winners - Raffle winners
             * @returns {object} Distribution results
             */
            distributeRafflePrizes(period, winners) {
                if (!this.tokenEconomyManager) {
                    console.error('TokenEconomyManager not set. Cannot distribute raffle prizes.');
                    return { success: false, reason: 'token_manager_not_set' };
                }
                
                const distributionResults = {
                    period,
                    timestamp: Date.now(),
                    type: 'raffle',
                    winners: [],
                    totalDistributed: 0
                };
                
                for (const winner of winners) {
                    if (winner.prizeTokens > 0) {
                        // Award tokens to raffle winner
                        const result = this.tokenEconomyManager.awardTokens(
                            winner.prizeTokens,
                            `${period}_raffle_prize`,
                            {
                                playerId: winner.playerId,
                                playerName: winner.playerName,
                                period: period,
                                rafflePosition: winner.rafflePosition,
                                score: winner.totalScore
                            }
                        );
                        
                        if (result.success) {
                            distributionResults.winners.push({
                                playerId: winner.playerId,
                                playerName: winner.playerName,
                                rafflePosition: winner.rafflePosition,
                                prizeTokens: winner.prizeTokens,
                                transactionId: result.transaction.id
                            });
                            
                            distributionResults.totalDistributed += winner.prizeTokens;
                            
                            // Notify raffle winner (Requirement 8.5)
                            this.notifyRaffleWinner(winner, period, winner.prizeTokens);
                            
                            console.log(`Distributed ${winner.prizeTokens} WISH tokens to ${period} raffle winner: ${winner.playerName}`);
                        } else {
                            console.error(`Failed to distribute raffle prize to ${winner.playerName}:`, result.reason);
                            distributionResults.winners.push({
                                playerId: winner.playerId,
                                playerName: winner.playerName,
                                rafflePosition: winner.rafflePosition,
                                prizeTokens: winner.prizeTokens,
                                error: result.reason
                            });
                        }
                    }
                }
                
                // Record distribution in history
                this.prizeDistributionHistory.push(distributionResults);
                
                // Trigger callback
                if (this.onRaffleWinnerSelectedCallback) {
                    this.onRaffleWinnerSelectedCallback(distributionResults);
                }
                
                return distributionResults;
            }
            
            /**
             * Notify contest winner of their prize (Requirement 8.5)
             * @param {object} winner - Winner data
             * @param {string} period - Contest period
             * @param {string} tier - Prize tier
             * @param {number} prizeTokens - Prize amount
             */
            notifyWinner(winner, period, tier, prizeTokens) {
                // This would integrate with a notification system
                // For now, we'll just log the notification
                const notification = {
                    type: 'contest_winner',
                    playerId: winner.playerId,
                    playerName: winner.playerName,
                    message: `Congratulations! You won ${tier.toUpperCase()} prize in the ${period} contest!`,
                    prizeTokens: prizeTokens,
                    period: period,
                    tier: tier,
                    position: winner.position,
                    timestamp: Date.now()
                };
                
                console.log(`Winner notification:`, notification);
                
                // Here you would integrate with your notification system
                // e.g., this.notificationManager.sendNotification(notification);
            }
            
            /**
             * Notify raffle winner of their prize (Requirement 8.5)
             * @param {object} winner - Winner data
             * @param {string} period - Contest period
             * @param {number} prizeTokens - Prize amount
             */
            notifyRaffleWinner(winner, period, prizeTokens) {
                // This would integrate with a notification system
                // For now, we'll just log the notification
                const notification = {
                    type: 'raffle_winner',
                    playerId: winner.playerId,
                    playerName: winner.playerName,
                    message: `Congratulations! You won the ${period} raffle!`,
                    prizeTokens: prizeTokens,
                    period: period,
                    rafflePosition: winner.rafflePosition,
                    timestamp: Date.now()
                };
                
                console.log(`Raffle winner notification:`, notification);
                
                // Here you would integrate with your notification system
                // e.g., this.notificationManager.sendNotification(notification);
            }
            
            /**
             * Get raffle winners for a period
             * @param {string} period - Contest period ('daily' or 'weekly')
             * @returns {Array} Raffle winners
             */
            getRaffleWinners(period) {
                return this.raffleWinners[period] || [];
            }
            
            /**
             * Get prize distribution history
             * @param {number} limit - Maximum number of records to return
             * @returns {Array} Prize distribution history
             */
            getPrizeDistributionHistory(limit = 10) {
                return this.prizeDistributionHistory.slice(-limit).reverse();
            }
            
            /**
             * Get prize pool for a period
             * @param {string} period - Contest period ('daily' or 'weekly')
             * @returns {number} Prize pool amount
             */
            getPrizePool(period) {
                return this.prizePools[period] || 0;
            }
            
            /**
             * Set callback for prize distribution
             * @param {function} callback - Callback function
             */
            setPrizeDistributedCallback(callback) {
                this.onPrizeDistributedCallback = callback;
            }
            
            /**
             * Set callback for raffle winner selection
             * @param {function} callback - Callback function
             */
            setRaffleWinnerSelectedCallback(callback) {
                this.onRaffleWinnerSelectedCallback = callback;
            }
    
    /**
     * Update method to be called regularly
     * @param {number} deltaTime - Time elapsed since last update
     */
    update(deltaTime) {
        // Update contest periods
        this.updateContestPeriods();
        
        // Update leaderboards periodically
        // This could be optimized to only update when necessary
        this.updateLeaderboards();
    }
    
    /**
     * Set debug mode
     * @param {boolean} enabled - Whether debug mode is enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`LeaderboardManager debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }
    
    /**
     * Export debug data
     * @returns {object} Debug data
     */
    exportDebugData() {
        return {
            participants: Array.from(this.participants.entries()),
            leaderboards: this.leaderboards,
            contestWinners: this.contestWinners,
            raffleQualifiedPlayers: {
                daily: Array.from(this.raffleQualifiedPlayers.daily),
                weekly: Array.from(this.raffleQualifiedPlayers.weekly)
            },
            raffleWinners: this.raffleWinners,
            prizePools: this.prizePools,
            prizeDistributionHistory: this.prizeDistributionHistory,
            contestPeriodSettings: this.contestPeriodSettings,
            currentContestPeriod: this.currentContestPeriod
        };
    }
}