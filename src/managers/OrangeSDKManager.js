import * as GGSDK from 'gg-game-sdk';

/**
 * OrangeSDKManager handles data persistence to Orange SDK for tournaments
 * Only saves data - does not load progress (game starts fresh each time)
 * Tracks special statuses that can affect gameplay (e.g., login streaks, bonuses)
 * In debug mode, uses localStorage instead of Orange SDK
 */
export class OrangeSDKManager {
    constructor() {
        // SDK state
        this.isSDKReady = false;
        this.isInitialized = false;
        
        // Debug mode detection
        this.isDebugMode = this.checkDebugMode();
        
        // Player progress data structure
        this.playerData = {
            // Core progress
            totalScore: 0,
            highestLevel: 0,
            wishTokensEarned: 0,
            wishTokensSpent: 0,
            wishTokensBalance: 0,
            
            // Level completion tracking
            levelsCompleted: [],
            levelBestScores: {},
            totalPlayTime: 0,
            
            // Performance metrics
            perfectCompletions: 0,
            totalEnemiesDefeated: 0,
            averageCompletionTime: 0,
            
            // Special statuses (affects gameplay)
            specialStatuses: {
                loginStreak: 0,
                lastLoginDate: null,
                bonusLivesEarned: 0,
                tournamentParticipant: false,
                achievementUnlocks: []
            },
            
            // Session data
            currentSession: {
                startTime: Date.now(),
                levelsPlayedThisSession: 0,
                tokensEarnedThisSession: 0,
                scoreThisSession: 0
            },
            
            // Metadata
            lastSaveTime: Date.now(),
            gameVersion: '1.0.0',
            totalSessions: 0
        };
        
        // Save operation state
        this.saveInProgress = false;
        this.pendingSaveData = null;
        this.saveRetryCount = 0;
        this.maxRetries = 3;
        this.retryDelay = 1000; // 1 second
        
        // Callbacks
        this.onDataSavedCallback = null;
        this.onSaveErrorCallback = null;
        this.onSpecialStatusCallback = null;
        
        // Auto-save configuration
        this.autoSaveEnabled = true;
        this.autoSaveInterval = 30000; // 30 seconds
        this.lastAutoSave = Date.now();
        
    }
    
    /**
     * Check if debug mode is enabled
     * @returns {boolean} True if debug mode is enabled
     */
    checkDebugMode() {
        // Check if AuthManager is available and has debug mode
        if (window.AuthManager && window.AuthManager.config && window.AuthManager.config.debugMode) {
            return true;
        }
        
        // Check for debug mode in URL parameters
        if (window.location.search.includes('debug=true')) {
            return true;
        }
        
        // Check for debug mode in window object
        if (window.DEBUG_MODE) {
            return true;
        }
        
        // Check for localhost development
        if (window.location.hostname === 'localhost' ||
            window.location.hostname === '127.0.0.1') {
            return true;
        }
        
        return false;
    }
    
    /**
     * Initialize the Orange SDK and set up event listeners
     */
    async initialize() {
        if (this.isInitialized) return;
        
        if (this.isDebugMode) {
            console.log('Debug mode detected - using localStorage instead of Orange SDK');
            this.isSDKReady = true;
            this.isInitialized = true;
            
            // Load data from localStorage if available
            this.loadFromLocalStorage();
            
            // Initialize session data
            this.playerData.currentSession.startTime = Date.now();
            this.playerData.totalSessions++;
            
            return;
        }
        
        try {
            // Initialize session data
            this.playerData.currentSession.startTime = Date.now();
            this.playerData.totalSessions++;
            
            // Set up SDK event listeners
            this.setupSDKEventListeners();
            
            // Check for special statuses on startup
            await this.checkSpecialStatuses();
            
            // Send game loaded event
            GGSDK.gameLoaded();
            
            this.isSDKReady = true;
            this.isInitialized = true;
            
            
            // Perform initial save to establish baseline
            await this.savePlayerData('game_start');
            
        } catch (error) {
            console.error('Failed to initialize OrangeSDKManager:', error);
            this.isSDKReady = false;
        }
    }
    
    /**
     * Set up SDK event listeners for pause/resume/quit
     */
    setupSDKEventListeners() {
        // Skip SDK event listeners in debug mode
        if (this.isDebugMode) {
            return;
        }
        
        // Listen for pause events from parent
        GGSDK.listenPaused(() => {
            this.handleGamePause();
        });
        
        // Listen for resume events from parent
        GGSDK.listenResumed(() => {
            this.handleGameResume();
        });
        
        // Listen for quit events from parent
        GGSDK.listenQuit(() => {
            this.handleGameQuit();
        });
    }
    
    /**
     * Check for special statuses that affect gameplay
     */
    async checkSpecialStatuses() {
        // In debug mode, skip special status checking from Orange SDK
        if (this.isDebugMode) {
            return;
        }
        
        try {
            // Get existing data to check login streak
            const defaultData = { specialStatuses: this.playerData.specialStatuses };
            
            // This is the only time we read data - to check special statuses
            GGSDK.getGameData(defaultData, (data) => {
                if (data && data.specialStatuses) {
                    const savedStatuses = data.specialStatuses;
                    const today = new Date().toDateString();
                    const lastLogin = savedStatuses.lastLoginDate;
                    
                    // Check login streak
                    if (lastLogin) {
                        const lastLoginDate = new Date(lastLogin);
                        const daysDiff = Math.floor((Date.now() - lastLoginDate.getTime()) / (1000 * 60 * 60 * 24));
                        
                        if (daysDiff === 1) {
                            // Consecutive day login
                            this.playerData.specialStatuses.loginStreak = (savedStatuses.loginStreak || 0) + 1;
                        } else if (daysDiff > 1) {
                            // Streak broken
                            this.playerData.specialStatuses.loginStreak = 1;
                        } else {
                            // Same day login
                            this.playerData.specialStatuses.loginStreak = savedStatuses.loginStreak || 1;
                        }
                    } else {
                        // First time login
                        this.playerData.specialStatuses.loginStreak = 1;
                    }
                    
                    // Update last login date
                    this.playerData.specialStatuses.lastLoginDate = today;
                    
                    // Copy other special statuses
                    this.playerData.specialStatuses.bonusLivesEarned = savedStatuses.bonusLivesEarned || 0;
                    this.playerData.specialStatuses.tournamentParticipant = savedStatuses.tournamentParticipant || false;
                    this.playerData.specialStatuses.achievementUnlocks = savedStatuses.achievementUnlocks || [];
                    
                    // Check for special status bonuses
                    this.processSpecialStatusBonuses();
                }
            });
            
        } catch (error) {
            console.error('Error checking special statuses:', error);
        }
    }
    
    /**
     * Process special status bonuses that affect gameplay
     */
    processSpecialStatusBonuses() {
        const streak = this.playerData.specialStatuses.loginStreak;
        
        // Award bonus lives for login streaks
        if (streak >= 2 && streak <= 7) {
            const bonusLives = Math.min(streak - 1, 3); // Max 3 bonus lives
            this.playerData.specialStatuses.bonusLivesEarned = bonusLives;
            
            
            
            // Trigger callback for game to apply bonus
            if (this.onSpecialStatusCallback) {
                this.onSpecialStatusCallback('bonus_lives', bonusLives);
            }
        }
        
        // Other special status processing can be added here
        if (this.playerData.specialStatuses.tournamentParticipant) {
            
            if (this.onSpecialStatusCallback) {
                this.onSpecialStatusCallback('tournament_participant', true);
            }
        }
    }
    
    /**
     * Update player progress data
     * @param {object} progressData - Progress data to update
     */
    updatePlayerProgress(progressData) {
        if (!progressData) return;
        
        // Update core progress
        if (progressData.score !== undefined) {
            this.playerData.totalScore = Math.max(this.playerData.totalScore, progressData.score);
            this.playerData.currentSession.scoreThisSession += progressData.scoreGained || 0;
        }
        
        if (progressData.level !== undefined) {
            this.playerData.highestLevel = Math.max(this.playerData.highestLevel, progressData.level);
        }
        
        if (progressData.tokensEarned !== undefined) {
            this.playerData.wishTokensEarned += progressData.tokensEarned;
            this.playerData.wishTokensBalance += progressData.tokensEarned;
            this.playerData.currentSession.tokensEarnedThisSession += progressData.tokensEarned;
        }
        
        if (progressData.tokensSpent !== undefined) {
            this.playerData.wishTokensSpent += progressData.tokensSpent;
            this.playerData.wishTokensBalance -= progressData.tokensSpent;
        }
        
        // Update level completion
        if (progressData.levelCompleted !== undefined) {
            const level = progressData.levelCompleted;
            if (!this.playerData.levelsCompleted.includes(level)) {
                this.playerData.levelsCompleted.push(level);
                this.playerData.currentSession.levelsPlayedThisSession++;
            }
            
            // Update best score for level
            if (progressData.levelScore !== undefined) {
                const currentBest = this.playerData.levelBestScores[level] || 0;
                this.playerData.levelBestScores[level] = Math.max(currentBest, progressData.levelScore);
            }
        }
        
        // Update performance metrics
        if (progressData.perfectCompletion) {
            this.playerData.perfectCompletions++;
        }
        
        if (progressData.enemiesDefeated !== undefined) {
            this.playerData.totalEnemiesDefeated += progressData.enemiesDefeated;
        }
        
        if (progressData.completionTime !== undefined) {
            // Update average completion time
            const totalLevels = this.playerData.levelsCompleted.length;
            if (totalLevels > 0) {
                this.playerData.averageCompletionTime = 
                    ((this.playerData.averageCompletionTime * (totalLevels - 1)) + progressData.completionTime) / totalLevels;
            }
        }
        
        // Update play time
        const currentTime = Date.now();
        const sessionTime = currentTime - this.playerData.currentSession.startTime;
        this.playerData.totalPlayTime += sessionTime;
        this.playerData.currentSession.startTime = currentTime;
        
        
    }
    
    /**
     * Load player data from localStorage (debug mode only)
     */
    loadFromLocalStorage() {
        if (!this.isDebugMode) return;
        
        try {
            const savedData = localStorage.getItem('orangeDefenseGameData');
            if (savedData) {
                const parsedData = JSON.parse(savedData);
                
                // Merge saved data with default structure
                Object.assign(this.playerData, parsedData);
                
            }
        } catch (error) {
            console.error('Failed to load data from localStorage:', error);
        }
    }
    
    /**
     * Save player data to localStorage (debug mode only)
     * @param {object} dataToSave - Data to save
     */
    saveToLocalStorage(dataToSave) {
        if (!this.isDebugMode) return;
        
        try {
            localStorage.setItem('orangeDefenseGameData', JSON.stringify(dataToSave));
        } catch (error) {
            console.error('Failed to save data to localStorage:', error);
        }
    }
    
    /**
     * Save player data to Orange SDK or localStorage (debug mode)
     * @param {string} reason - Reason for saving
     * @param {boolean} force - Force save even if one is in progress
     */
    async savePlayerData(reason = 'manual', force = false) {
        if (!this.isSDKReady) {
            console.warn('SDK not ready, cannot save data');
            return false;
        }
        
        if (this.saveInProgress && !force) {
            this.pendingSaveData = { ...this.playerData };
            return false;
        }
        
        this.saveInProgress = true;
        this.playerData.lastSaveTime = Date.now();
        
        const dataToSave = {
            ...this.playerData,
            saveReason: reason,
            saveTimestamp: Date.now()
        };
        
        // Handle debug mode with localStorage
        if (this.isDebugMode) {
            this.saveToLocalStorage(dataToSave);
            
            
            // Trigger success callback
            if (this.onDataSavedCallback) {
                this.onDataSavedCallback(dataToSave, reason);
            }
            
            // Process any pending save
            if (this.pendingSaveData) {
                const pendingData = this.pendingSaveData;
                this.pendingSaveData = null;
                setTimeout(() => this.savePlayerData('pending_update'), 100);
            }
            
            this.saveInProgress = false;
            return true;
        }
        
        // Normal Orange SDK save operation
        try {
            await this.performSaveWithRetry(dataToSave);
            
            
            // Trigger success callback
            if (this.onDataSavedCallback) {
                this.onDataSavedCallback(dataToSave, reason);
            }
            
            // Process any pending save
            if (this.pendingSaveData) {
                const pendingData = this.pendingSaveData;
                this.pendingSaveData = null;
                setTimeout(() => this.savePlayerData('pending_update'), 100);
            }
            
            return true;
            
        } catch (error) {
            console.error('Failed to save player data:', error);
            
            // Trigger error callback
            if (this.onSaveErrorCallback) {
                this.onSaveErrorCallback(error, reason);
            }
            
            return false;
        } finally {
            this.saveInProgress = false;
            this.saveRetryCount = 0;
        }
    }

    /**
     * Perform save operation with retry logic
     * @param {object} dataToSave - Data to save
     */
    async performSaveWithRetry(dataToSave) {
        for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
            try {
                // Use Promise wrapper for SDK save operation
                await new Promise((resolve, reject) => {
                    try {
                        GGSDK.saveGameData(dataToSave);
                        // SDK doesn't provide callback, assume success after short delay
                        setTimeout(resolve, 100);
                    } catch (error) {
                        reject(error);
                    }
                });

                return; // Success

            } catch (error) {
                this.saveRetryCount = attempt + 1;

                if (attempt < this.maxRetries) {
                    console.warn(`Save attempt ${attempt + 1} failed, retrying in ${this.retryDelay}ms:`, error);
                    await new Promise(resolve => setTimeout(resolve, this.retryDelay));
                    this.retryDelay *= 2; // Exponential backoff
                } else {
                    throw error; // Final attempt failed
                }
            }
        }
    }

    /**
     * Handle level completion - automatic save trigger
     * @param {object} completionData - Level completion data
     */
    async onLevelCompleted(completionData) {
        if (!completionData) return;

        // Update progress data
        this.updatePlayerProgress({
            level: completionData.levelNumber,
            levelCompleted: completionData.levelNumber,
            levelScore: completionData.score?.totalScore || 0,
            scoreGained: completionData.score?.totalScore || 0,
            tokensEarned: completionData.score?.tokenReward || 0,
            perfectCompletion: completionData.perfectCompletion,
            enemiesDefeated: completionData.enemiesDefeated,
            completionTime: completionData.completionTime
        });

        // Send game over event to SDK with score (only if not in debug mode)
        if (!this.isDebugMode) {
            const finalScore = completionData.score?.totalScore || 0;
            GGSDK.gameOver(finalScore);
        }

        // Auto-save progress
        await this.savePlayerData('level_completed');

    }

    /**
     * Handle token changes - automatic save trigger
     * @param {object} tokenData - Token change data
     */
    async onTokensChanged(tokenData) {
        if (!tokenData) return;

        // Update progress data
        this.updatePlayerProgress({
            tokensEarned: tokenData.earned || 0,
            tokensSpent: tokenData.spent || 0
        });

        // Auto-save if significant token change
        const significantChange = (tokenData.earned || 0) + (tokenData.spent || 0) >= 50;
        if (significantChange) {
            await this.savePlayerData('token_change');
        }
    }

    /**
     * Handle game pause event
     */
    handleGamePause() {
        // Send pause event to SDK (only if not in debug mode)
        if (!this.isDebugMode) {
            GGSDK.gamePaused();
        }

        // Save current progress
        this.savePlayerData('game_paused');
    }

    /**
     * Handle game resume event
     */
    handleGameResume() {
        // Send resume event to SDK (only if not in debug mode)
        if (!this.isDebugMode) {
            GGSDK.gameResumed();
        }

        // Update session start time
        this.playerData.currentSession.startTime = Date.now();
    }

    /**
     * Handle game quit event
     */
    async handleGameQuit() {
        // Update final session data
        const sessionTime = Date.now() - this.playerData.currentSession.startTime;
        this.playerData.totalPlayTime += sessionTime;

        // Send final game over event (only if not in debug mode)
        if (!this.isDebugMode) {
            GGSDK.gameOver(this.playerData.totalScore);
        }

        // Final save before quit
        await this.savePlayerData('game_quit');

    }

    /**
     * Update auto-save if enabled and interval has passed
     */
    updateAutoSave() {
        if (!this.autoSaveEnabled || !this.isSDKReady) return;

        const now = Date.now();
        if (now - this.lastAutoSave >= this.autoSaveInterval) {
            this.savePlayerData('auto_save');
            this.lastAutoSave = now;
        }
    }

    /**
     * Get current player data (for debugging/display)
     * @returns {object} Current player data
     */
    getPlayerData() {
        return { ...this.playerData };
    }

    /**
     * Get special status information
     * @returns {object} Special status data
     */
    getSpecialStatuses() {
        return { ...this.playerData.specialStatuses };
    }

    /**
     * Add achievement unlock
     * @param {string} achievementId - Achievement identifier
     */
    unlockAchievement(achievementId) {
        if (!this.playerData.specialStatuses.achievementUnlocks.includes(achievementId)) {
            this.playerData.specialStatuses.achievementUnlocks.push(achievementId);
            this.savePlayerData('achievement_unlock');


            if (this.onSpecialStatusCallback) {
                this.onSpecialStatusCallback('achievement_unlock', achievementId);
            }
        }
    }

    /**
     * Set tournament participant status
     * @param {boolean} isParticipant - Tournament participation status
     */
    setTournamentParticipant(isParticipant) {
        this.playerData.specialStatuses.tournamentParticipant = isParticipant;
        this.savePlayerData('tournament_status');

        if (this.onSpecialStatusCallback) {
            this.onSpecialStatusCallback('tournament_participant', isParticipant);
        }
    }

    /**
     * Set callback for when data is saved
     * @param {function} callback - Callback function
     */
    setOnDataSavedCallback(callback) {
        this.onDataSavedCallback = callback;
    }

    /**
     * Set callback for save errors
     * @param {function} callback - Callback function
     */
    setOnSaveErrorCallback(callback) {
        this.onSaveErrorCallback = callback;
    }

    /**
     * Set callback for special status changes
     * @param {function} callback - Callback function
     */
    setOnSpecialStatusCallback(callback) {
        this.onSpecialStatusCallback = callback;
    }

    /**
     * Enable or disable auto-save
     * @param {boolean} enabled - Auto-save enabled state
     * @param {number} interval - Auto-save interval in milliseconds
     */
    setAutoSave(enabled, interval = 30000) {
        this.autoSaveEnabled = enabled;
        this.autoSaveInterval = interval;

        if (enabled) {
            this.lastAutoSave = Date.now();
        }

    }

    /**
     * Get save statistics
     * @returns {object} Save operation statistics
     */
    getSaveStatistics() {
        return {
            isSDKReady: this.isSDKReady,
            saveInProgress: this.saveInProgress,
            saveRetryCount: this.saveRetryCount,
            lastSaveTime: this.playerData.lastSaveTime,
            autoSaveEnabled: this.autoSaveEnabled,
            autoSaveInterval: this.autoSaveInterval,
            lastAutoSave: this.lastAutoSave
        };
    }

    /**
     * Reset manager state (for testing)
     */
    reset() {
        this.playerData = {
            totalScore: 0,
            highestLevel: 0,
            wishTokensEarned: 0,
            wishTokensSpent: 0,
            wishTokensBalance: 0,
            levelsCompleted: [],
            levelBestScores: {},
            totalPlayTime: 0,
            perfectCompletions: 0,
            totalEnemiesDefeated: 0,
            averageCompletionTime: 0,
            specialStatuses: {
                loginStreak: 0,
                lastLoginDate: null,
                bonusLivesEarned: 0,
                tournamentParticipant: false,
                achievementUnlocks: []
            },
            currentSession: {
                startTime: Date.now(),
                levelsPlayedThisSession: 0,
                tokensEarnedThisSession: 0,
                scoreThisSession: 0
            },
            lastSaveTime: Date.now(),
            gameVersion: '1.0.0',
            totalSessions: 0
        };

        this.saveInProgress = false;
        this.pendingSaveData = null;
        this.saveRetryCount = 0;
        this.retryDelay = 1000;

    }
}
