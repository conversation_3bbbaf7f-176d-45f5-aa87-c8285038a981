import { GAME_CONFIG, ENVIRONMENT_TYPES } from '../config/gameConfig.js';
import { Vector2 } from '../utils/Vector2.js';

/**
 * LevelManager handles level progression, configuration, and completion detection
 * Manages scoring system based on enemies defeated and completion time
 */
export class LevelManager {
    constructor(gameObjectManager = null) {
        this.gameObjectManager = gameObjectManager;
        
        // Level state
        this.currentLevel = 1;
        this.levelInProgress = false;
        this.levelStartTime = 0;
        this.levelCompletionTime = 0;
        
        // Level configuration
        this.levelConfig = null;
        this.maxLevels = 50; // Can be expanded
        
        // Scoring system
        this.currentScore = 0;
        this.levelScore = 0;
        this.enemiesDefeated = 0;
        this.levelEnemiesDefeated = 0;
        this.totalScore = 0;
        
        // Performance tracking for token rewards
        this.levelStartScore = 0;
        this.perfectCompletion = true; // No damage taken
        this.speedBonus = 0;
        this.accuracyBonus = 0;
        
        // Level completion criteria
        this.requiredEnemiesDefeated = 0;
        this.wavesCompleted = 0;
        this.requiredWaves = 0;
        
        // Callbacks for level events
        this.onLevelStartCallback = null;
        this.onLevelCompleteCallback = null;
        this.onScoreUpdateCallback = null;
        
    }
    
    /**
     * Start a specific level
     * @param {number} levelNumber - Level number to start
     * @returns {object} Level configuration
     */
    startLevel(levelNumber = null) {
        if (levelNumber !== null) {
            this.currentLevel = levelNumber;
        }
        
        // Generate level configuration
        this.levelConfig = this.generateLevelConfig(this.currentLevel);
        
        // Reset level state
        this.levelInProgress = true;
        this.levelStartTime = performance.now();
        this.levelCompletionTime = 0;
        this.levelScore = 0;
        this.levelEnemiesDefeated = 0;
        this.levelStartScore = this.currentScore;
        this.perfectCompletion = true;
        this.speedBonus = 0;
        this.accuracyBonus = 0;
        
        // Set completion criteria
        this.requiredEnemiesDefeated = this.levelConfig.totalEnemies;
        this.requiredWaves = this.levelConfig.totalWaves;
        this.wavesCompleted = 0;
        
        
        // Check for pending Reality Warp environment and apply it
        if (window.gameEngine && window.gameEngine.realityWarpManager) {
            const realityWarpManager = window.gameEngine.realityWarpManager;
            
            // Get and clear the pending environment if it exists
            const pendingEnvironment = realityWarpManager.getAndClearPendingEnvironment();
            
            if (pendingEnvironment) {
                
                // Store environment data in level configuration
                this.levelConfig.environmentData = {
                    type: pendingEnvironment.type || 'custom',
                    name: pendingEnvironment.name || 'Custom Environment',
                    description: pendingEnvironment.description || 'Custom environment from Reality Warp',
                    gameplayModifiers: pendingEnvironment.gameplayModifiers || {},
                    imageData: pendingEnvironment.imageData || null,
                    imageUrl: pendingEnvironment.imageUrl || null
                };
                
            } else {
            }
        }
        
        // Trigger level start callback
        if (this.onLevelStartCallback) {
            this.onLevelStartCallback(this.currentLevel, this.levelConfig);
        }
        
        return this.levelConfig;
    }
    
    /**
     * Generate level configuration based on level number
     * @param {number} levelNumber - Level number
     * @returns {object} Level configuration
     */
    generateLevelConfig(levelNumber) {
        // Base difficulty scaling
        const difficultyMultiplier = Math.min(3.0, 1.0 + (levelNumber - 1) * 0.1);
        
        // Wave configuration
        const baseWaveCount = 3;
        const waveIncrease = Math.floor((levelNumber - 1) / 3);
        const totalWaves = Math.min(8, baseWaveCount + waveIncrease);
        
        // Calculate total enemies by summing up enemies from all waves
        let totalEnemies = 0;
        for (let wave = 1; wave <= totalWaves; wave++) {
            // Use the same calculation as EnemyManager.generateWaveConfig()
            const waveBaseEnemyCount = 5;
            const waveEnemyCountIncrease = Math.floor(wave / 2);
            const waveTotalEnemies = waveBaseEnemyCount + waveEnemyCountIncrease;
            totalEnemies += waveTotalEnemies;
        }
        
        // Apply difficulty multiplier to the total
        totalEnemies = Math.floor(totalEnemies * difficultyMultiplier);
        
        // Environment selection
        const environment = this.selectLevelEnvironment(levelNumber);
        
        // Boss levels (every 10th level)
        const hasBoss = levelNumber % 10 === 0;
        
        // Special mechanics (every 5th level)
        const hasSpecialMechanics = levelNumber % 5 === 0;
        
        // Time limits for scoring
        const baseTimeLimit = 120; // 2 minutes base
        const timeLimitAdjustment = Math.min(60, levelNumber * 2);
        const timeLimit = baseTimeLimit + timeLimitAdjustment;
        
        // Score targets
        const baseScoreTarget = 1000;
        const scoreTarget = Math.floor(baseScoreTarget * difficultyMultiplier * levelNumber);
        
        return {
            levelNumber: levelNumber,
            difficulty: Math.min(10, Math.floor(levelNumber / 5) + 1),
            totalEnemies: totalEnemies,
            totalWaves: totalWaves,
            environment: environment,
            hasBoss: hasBoss,
            hasSpecialMechanics: hasSpecialMechanics,
            timeLimit: timeLimit,
            scoreTarget: scoreTarget,
            completionReward: this.calculateBaseLevelReward(levelNumber),
            difficultyMultiplier: difficultyMultiplier,
            
            // Enemy distribution
            enemyDistribution: this.generateEnemyDistribution(levelNumber, environment),
            
            // Special conditions
            conditions: this.generateLevelConditions(levelNumber),
            
            // Visual/Audio settings
            backgroundMusic: this.selectBackgroundMusic(levelNumber, environment),
            visualEffects: this.selectVisualEffects(levelNumber, environment)
        };
    }
    
    /**
     * Select environment for the level
     * @param {number} levelNumber - Level number
     * @returns {string} Environment type
     */
    selectLevelEnvironment(levelNumber) {
        // Early levels in space
        if (levelNumber <= 5) {
            return ENVIRONMENT_TYPES.SPACE;
        }
        
        // Introduce variety gradually
        const environments = [
            ENVIRONMENT_TYPES.SPACE,
            ENVIRONMENT_TYPES.UNDERWATER,
            ENVIRONMENT_TYPES.VOLCANIC,
            ENVIRONMENT_TYPES.CRYSTAL,
            ENVIRONMENT_TYPES.FOREST,
            ENVIRONMENT_TYPES.DESERT,
            ENVIRONMENT_TYPES.ICE
        ];
        
        // Boss levels have special environments
        if (levelNumber % 10 === 0) {
            const bossEnvironments = [
                ENVIRONMENT_TYPES.VOLCANIC,
                ENVIRONMENT_TYPES.CRYSTAL,
                ENVIRONMENT_TYPES.ICE
            ];
            return bossEnvironments[Math.floor((levelNumber / 10 - 1) % bossEnvironments.length)];
        }
        
        // Cycle through environments with some randomness
        const baseIndex = Math.floor((levelNumber - 6) / 3) % environments.length;
        const variation = (levelNumber + 7) % 3; // Add some variation
        const finalIndex = (baseIndex + variation) % environments.length;
        
        return environments[finalIndex];
    }
    
    /**
     * Generate enemy distribution for the level
     * @param {number} levelNumber - Level number
     * @param {string} environment - Level environment
     * @returns {object} Enemy distribution configuration
     */
    generateEnemyDistribution(levelNumber, environment) {
        const distribution = {};
        
        // Base distribution varies by level progression
        if (levelNumber <= 3) {
            // Early levels - simple enemies
            distribution.basic = 0.7;
            distribution.advanced = 0.3;
            distribution.elite = 0.0;
        } else if (levelNumber <= 10) {
            // Mid levels - introduce variety
            distribution.basic = 0.5;
            distribution.advanced = 0.4;
            distribution.elite = 0.1;
        } else {
            // Later levels - more challenging
            distribution.basic = 0.3;
            distribution.advanced = 0.5;
            distribution.elite = 0.2;
        }
        
        // Environment affects enemy types
        distribution.environmentalBonus = this.getEnvironmentalEnemyBonus(environment);
        
        return distribution;
    }
    
    /**
     * Get environmental enemy bonus
     * @param {string} environment - Environment type
     * @returns {object} Environmental bonus configuration
     */
    getEnvironmentalEnemyBonus(environment) {
        const bonuses = {
            [ENVIRONMENT_TYPES.SPACE]: { air: 1.2, crystal: 1.1 },
            [ENVIRONMENT_TYPES.UNDERWATER]: { water: 1.5, earth: 0.8 },
            [ENVIRONMENT_TYPES.VOLCANIC]: { fire: 1.6, earth: 1.3 },
            [ENVIRONMENT_TYPES.CRYSTAL]: { crystal: 1.8, shadow: 1.2 },
            [ENVIRONMENT_TYPES.FOREST]: { earth: 1.4, shadow: 1.3 },
            [ENVIRONMENT_TYPES.DESERT]: { fire: 1.2, earth: 1.1 },
            [ENVIRONMENT_TYPES.ICE]: { water: 1.3, fire: 0.5, air: 0.7 }
        };
        
        return bonuses[environment] || {};
    }
    
    /**
     * Generate level conditions and objectives
     * @param {number} levelNumber - Level number
     * @returns {object} Level conditions
     */
    generateLevelConditions(levelNumber) {
        const conditions = {
            primary: 'defeat_all_enemies',
            secondary: [],
            bonus: []
        };
        
        // Add secondary objectives based on level
        if (levelNumber >= 3) {
            conditions.secondary.push('complete_under_time_limit');
        }
        
        if (levelNumber >= 5) {
            conditions.secondary.push('maintain_accuracy_above_70');
        }
        
        if (levelNumber >= 7) {
            conditions.secondary.push('take_minimal_damage');
        }
        
        // Bonus objectives for extra rewards
        conditions.bonus.push('perfect_accuracy');
        conditions.bonus.push('speed_completion');
        conditions.bonus.push('no_damage_taken');
        
        return conditions;
    }
    
    /**
     * Select background music for level
     * @param {number} levelNumber - Level number
     * @param {string} environment - Environment type
     * @returns {string} Music track identifier
     */
    selectBackgroundMusic(levelNumber, environment) {
        // Boss levels have special music
        if (levelNumber % 10 === 0) {
            return 'boss_theme';
        }
        
        // Environment-based music
        const musicMap = {
            [ENVIRONMENT_TYPES.SPACE]: 'space_ambient',
            [ENVIRONMENT_TYPES.UNDERWATER]: 'underwater_theme',
            [ENVIRONMENT_TYPES.VOLCANIC]: 'volcanic_intensity',
            [ENVIRONMENT_TYPES.CRYSTAL]: 'crystal_harmony',
            [ENVIRONMENT_TYPES.FOREST]: 'forest_mystery',
            [ENVIRONMENT_TYPES.DESERT]: 'desert_winds',
            [ENVIRONMENT_TYPES.ICE]: 'ice_caverns'
        };
        
        return musicMap[environment] || 'default_theme';
    }
    
    /**
     * Select visual effects for level
     * @param {number} levelNumber - Level number
     * @param {string} environment - Environment type
     * @returns {Array} Visual effects configuration
     */
    selectVisualEffects(levelNumber, environment) {
        const effects = [];
        
        // Environment-based effects
        switch (environment) {
            case ENVIRONMENT_TYPES.UNDERWATER:
                effects.push('water_bubbles', 'light_rays');
                break;
            case ENVIRONMENT_TYPES.VOLCANIC:
                effects.push('lava_particles', 'heat_distortion');
                break;
            case ENVIRONMENT_TYPES.CRYSTAL:
                effects.push('crystal_reflections', 'energy_pulses');
                break;
            case ENVIRONMENT_TYPES.FOREST:
                effects.push('floating_spores', 'dappled_light');
                break;
            case ENVIRONMENT_TYPES.DESERT:
                effects.push('sand_particles', 'heat_shimmer');
                break;
            case ENVIRONMENT_TYPES.ICE:
                effects.push('snow_particles', 'ice_crystals');
                break;
            default:
                effects.push('star_field', 'nebula_clouds');
        }
        
        // Add intensity effects for higher levels
        if (levelNumber >= 10) {
            effects.push('intensity_overlay');
        }
        
        return effects;
    }
    
    /**
     * Update level progress and check completion
     * @param {number} deltaTime - Time elapsed since last update
     * @param {object} gameState - Current game state
     */
    update(deltaTime, gameState = {}) {
        if (!this.levelInProgress) return;
        
        // Update level timer
        this.levelCompletionTime = performance.now() - this.levelStartTime;
        
        // Check for level completion
        this.checkLevelCompletion(gameState);
        
        // Update performance metrics
        this.updatePerformanceMetrics(gameState);
    }
    
    /**
     * Check if level completion criteria are met
     * @param {object} gameState - Current game state
     */
    checkLevelCompletion(gameState) {
        if (!this.levelInProgress || !this.levelConfig) return;
        
        // Primary completion criteria
        const enemiesCompleted = this.levelEnemiesDefeated >= this.requiredEnemiesDefeated;
        const wavesCompleted = this.wavesCompleted >= this.requiredWaves;
        
        // Check if level is complete
        if (enemiesCompleted && wavesCompleted) {
            this.completeLevel();
        }
        
        // Check for failure conditions
        const timeExpired = this.levelCompletionTime > (this.levelConfig.timeLimit * 1000);
        const playerDestroyed = gameState.playerDestroyed || false;
        
        if (timeExpired || playerDestroyed) {
            this.failLevel(timeExpired ? 'time_expired' : 'player_destroyed');
        }
    }
    
    /**
     * Complete the current level
     */
    completeLevel() {
        if (!this.levelInProgress) return;
        
        this.levelInProgress = false;
        const completionTimeSeconds = this.levelCompletionTime / 1000;
        
        // Calculate final score and bonuses
        const scoreData = this.calculateLevelScore(completionTimeSeconds);
        
        
        // Update total score
        this.currentScore += scoreData.totalScore;
        this.totalScore = this.currentScore;
        
        // Prepare completion data
        const completionData = {
            levelNumber: this.currentLevel,
            completed: true,
            completionTime: completionTimeSeconds,
            score: scoreData,
            enemiesDefeated: this.levelEnemiesDefeated,
            perfectCompletion: this.perfectCompletion,
            bonuses: scoreData.bonuses,
            nextLevel: this.currentLevel + 1
        };
        
        // Trigger completion callback
        if (this.onLevelCompleteCallback) {
            this.onLevelCompleteCallback(completionData);
        }
        
        // End reality warp when level is completed
        if (window.gameEngine && window.gameEngine.realityWarpManager) {
            const realityWarpManager = window.gameEngine.realityWarpManager;
            const warpState = realityWarpManager.getWarpState();
            if (warpState.status === 'active') {
                realityWarpManager.endWarp();
            }
        }
        
        return completionData;
    }
    
    /**
     * Fail the current level
     * @param {string} reason - Reason for failure
     */
    failLevel(reason) {
        if (!this.levelInProgress) return;
        
        this.levelInProgress = false;
        const completionTimeSeconds = this.levelCompletionTime / 1000;
        
        
        // Prepare failure data
        const failureData = {
            levelNumber: this.currentLevel,
            completed: false,
            reason: reason,
            completionTime: completionTimeSeconds,
            score: this.levelScore,
            enemiesDefeated: this.levelEnemiesDefeated,
            canRetry: true
        };
        
        // Trigger completion callback with failure data
        if (this.onLevelCompleteCallback) {
            this.onLevelCompleteCallback(failureData);
        }
        
        // End reality warp when level is failed
        if (window.gameEngine && window.gameEngine.realityWarpManager) {
            const realityWarpManager = window.gameEngine.realityWarpManager;
            const warpState = realityWarpManager.getWarpState();
            if (warpState.status === 'active') {
                realityWarpManager.endWarp();
            }
        }
        
        return failureData;
    }
    
    /**
     * Calculate level score based on performance
     * @param {number} completionTime - Time taken to complete level in seconds
     * @returns {object} Score breakdown
     */
    calculateLevelScore(completionTime) {
        const config = this.levelConfig;
        
        // Base score from enemies defeated
        const enemyScore = this.levelScore;
        
        // Time bonus (faster completion = higher bonus)
        const timeBonus = this.calculateTimeBonus(completionTime, config.timeLimit);
        
        // Accuracy bonus
        const accuracyBonus = this.calculateAccuracyBonus();
        
        // Perfect completion bonus
        const perfectBonus = this.perfectCompletion ? Math.floor(enemyScore * 0.5) : 0;
        
        // Level completion bonus
        const completionBonus = config.completionReward;
        
        // Difficulty multiplier
        const difficultyMultiplier = config.difficultyMultiplier;
        
        // Calculate total before multiplier
        const baseTotal = enemyScore + timeBonus + accuracyBonus + perfectBonus + completionBonus;
        
        // Apply difficulty multiplier
        const totalScore = Math.floor(baseTotal * difficultyMultiplier);
        
        return {
            enemyScore: enemyScore,
            timeBonus: timeBonus,
            accuracyBonus: accuracyBonus,
            perfectBonus: perfectBonus,
            completionBonus: completionBonus,
            difficultyMultiplier: difficultyMultiplier,
            totalScore: totalScore,
            bonuses: {
                speed: timeBonus > 0,
                accuracy: accuracyBonus > 0,
                perfect: perfectBonus > 0
            }
        };
    }
    
    /**
     * Calculate time bonus based on completion speed
     * @param {number} completionTime - Actual completion time in seconds
     * @param {number} timeLimit - Time limit in seconds
     * @returns {number} Time bonus score
     */
    calculateTimeBonus(completionTime, timeLimit) {
        const targetTime = timeLimit * 0.6; // Target is 60% of time limit
        
        if (completionTime <= targetTime) {
            // Excellent time - full bonus
            const maxBonus = 500;
            const timeRatio = completionTime / targetTime;
            return Math.floor(maxBonus * (2 - timeRatio)); // Bonus decreases as time increases
        } else if (completionTime <= timeLimit * 0.8) {
            // Good time - partial bonus
            return 200;
        } else if (completionTime <= timeLimit) {
            // Acceptable time - small bonus
            return 50;
        }
        
        return 0; // No bonus for slow completion
    }
    
    /**
     * Calculate accuracy bonus (placeholder - would need projectile tracking)
     * @returns {number} Accuracy bonus score
     */
    calculateAccuracyBonus() {
        // This would be calculated based on shots fired vs hits
        // For now, return a placeholder value
        return this.accuracyBonus;
    }
    
    /**
     * Update performance metrics during gameplay
     * @param {object} gameState - Current game state
     */
    updatePerformanceMetrics(gameState) {
        // Track if player has taken damage
        if (gameState.playerDamageTaken) {
            this.perfectCompletion = false;
        }
        
        // Update accuracy tracking (would need more detailed implementation)
        if (gameState.shotsFired && gameState.shotsHit) {
            const accuracy = gameState.shotsHit / gameState.shotsFired;
            if (accuracy >= 0.9) {
                this.accuracyBonus = 300;
            } else if (accuracy >= 0.7) {
                this.accuracyBonus = 150;
            } else if (accuracy >= 0.5) {
                this.accuracyBonus = 50;
            }
        }
    }
    
    /**
     * Record enemy defeat for scoring
     * @param {object} enemy - Defeated enemy
     * @param {number} scoreValue - Score value of the enemy
     */
    recordEnemyDefeat(enemy, scoreValue) {
        this.enemiesDefeated++;
        this.levelEnemiesDefeated++;
        this.levelScore += scoreValue;
        this.currentScore += scoreValue;
        
        // Trigger score update callback
        if (this.onScoreUpdateCallback) {
            this.onScoreUpdateCallback({
                enemiesDefeated: this.enemiesDefeated,
                levelEnemiesDefeated: this.levelEnemiesDefeated,
                currentScore: this.currentScore,
                levelScore: this.levelScore,
                scoreGained: scoreValue
            });
        }
    }
    
    /**
     * Record wave completion
     * @param {number} waveNumber - Completed wave number
     * @param {number} waveBonus - Bonus score for wave completion
     */
    recordWaveCompletion(waveNumber, waveBonus = 0) {
        this.wavesCompleted++;
        
        if (waveBonus > 0) {
            this.levelScore += waveBonus;
            this.currentScore += waveBonus;
        }
        
        
        // Trigger score update callback
        if (this.onScoreUpdateCallback) {
            this.onScoreUpdateCallback({
                wavesCompleted: this.wavesCompleted,
                currentScore: this.currentScore,
                levelScore: this.levelScore,
                scoreGained: waveBonus
            });
        }
    }
    
    /**
     * Calculate base level reward for token economy
     * @param {number} levelNumber - Level number
     * @returns {number} Base reward amount
     */
    calculateBaseLevelReward(levelNumber) {
        const baseReward = GAME_CONFIG.BASE_LEVEL_REWARD;
        const levelMultiplier = Math.floor(levelNumber / 5) + 1;
        return baseReward * levelMultiplier;
    }
    
    /**
     * Get current level status
     * @returns {object} Level status information
     */
    getLevelStatus() {
        return {
            currentLevel: this.currentLevel,
            levelInProgress: this.levelInProgress,
            levelConfig: this.levelConfig,
            completionTime: this.levelCompletionTime,
            score: {
                current: this.currentScore,
                level: this.levelScore,
                total: this.totalScore
            },
            progress: {
                enemiesDefeated: this.levelEnemiesDefeated,
                requiredEnemies: this.requiredEnemiesDefeated,
                wavesCompleted: this.wavesCompleted,
                requiredWaves: this.requiredWaves
            },
            performance: {
                perfectCompletion: this.perfectCompletion,
                speedBonus: this.speedBonus,
                accuracyBonus: this.accuracyBonus
            }
        };
    }
    
    /**
     * Reset level manager state
     */
    reset() {
        this.currentLevel = 1;
        this.levelInProgress = false;
        this.levelStartTime = 0;
        this.levelCompletionTime = 0;
        this.currentScore = 0;
        this.levelScore = 0;
        this.enemiesDefeated = 0;
        this.levelEnemiesDefeated = 0;
        this.totalScore = 0;
        this.perfectCompletion = true;
        this.speedBonus = 0;
        this.accuracyBonus = 0;
        this.wavesCompleted = 0;
        
    }
    
    /**
     * Set callback for level start events
     * @param {Function} callback - Callback function
     */
    setOnLevelStart(callback) {
        this.onLevelStartCallback = callback;
    }
    
    /**
     * Set callback for level completion events
     * @param {Function} callback - Callback function
     */
    setOnLevelComplete(callback) {
        this.onLevelCompleteCallback = callback;
    }
    
    /**
     * Set callback for score update events
     * @param {Function} callback - Callback function
     */
    setOnScoreUpdate(callback) {
        this.onScoreUpdateCallback = callback;
    }
    
    /**
     * Set environment data for the current level
     * @param {object} environmentData - Environment data to set
     */
    setEnvironmentData(environmentData) {
        if (this.levelConfig) {
            this.levelConfig.environmentData = environmentData;
        } else {
            console.warn('Cannot set environment data: no active level configuration');
        }
    }
}