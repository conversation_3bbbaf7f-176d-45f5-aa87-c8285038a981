import { GAME_CONFIG } from '../config/gameConfig.js';
import { Vector2 } from '../utils/Vector2.js';

/**
 * PowerUp base class for all power-up types
 * Provides common functionality for power-up effects, duration tracking, and application
 */
export class PowerUp {
    constructor(type, cost, duration = null, description = '') {
        this.type = type;
        this.cost = cost;
        this.duration = duration; // Use the duration passed to constructor
        this.description = description;
        this.isActive = false;
        this.timeRemaining = duration;
        this.appliedAt = null;
        this.id = PowerUp.generateId();
        
        // Visual properties
        this.icon = null;
        this.color = '#00ffff';
        this.glowColor = '#ffffff';
    }
    
    // Static ID generator
    static idCounter = 0;
    static generateId() {
        return `powerup_${++PowerUp.idCounter}`;
    }
    
    /**
     * Apply the power-up effect to the player ship
     * @param {PlayerShip} playerShip - The player ship to apply the effect to
     * @returns {Promise<boolean>|boolean} True if successfully applied
     */
    async apply(playerShip) {
        if (this.isActive) {
            console.warn(`PowerUp ${this.type} is already active`);
            return false;
        }

        this.isActive = true;
        this.appliedAt = Date.now();
        
        // Only set timeRemaining to null if duration is 0 (permanent power-ups)
        // Otherwise, use the duration passed to the constructor
        this.timeRemaining = this.duration === 0 ? null : this.duration;

        
        const result = await this.applyEffect(playerShip);

        // If application failed, reset the active state
        if (!result) {
            this.isActive = false;
            this.appliedAt = null;
            this.timeRemaining = null;
        }

        return result;
    }
    
    /**
     * Remove the power-up effect from the player ship
     * @param {PlayerShip} playerShip - The player ship to remove the effect from
     * @returns {boolean} True if successfully removed
     */
    remove(playerShip) {
        if (!this.isActive) {
            console.warn(`PowerUp ${this.type} is not active`);
            return false;
        }
        
        this.isActive = false;
        this.timeRemaining = null;
        
        
        return this.removeEffect(playerShip);
    }
    
    /**
     * Update the power-up (handle duration countdown)
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     * @param {PlayerShip} playerShip - The player ship (for auto-removal)
     * @returns {boolean} True if power-up is still active
     */
    update(deltaTime, playerShip) {
        if (!this.isActive) return false;
        
        // Handle duration countdown for power-ups with duration
        if (this.timeRemaining !== null) {
            this.timeRemaining -= deltaTime;
            
            // Remove power-up if duration expires
            if (this.timeRemaining <= 0) {
                this.remove(playerShip);
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Get the remaining time as a percentage (0-1)
     * @returns {number} Percentage of time remaining
     */
    getTimeRemainingPercentage() {
        if (this.duration === null) return 1; // Permanent power-ups
        if (!this.isActive) return 0;
        return Math.max(0, this.timeRemaining / this.duration);
    }
    
    /**
     * Get formatted time remaining string
     * @returns {string} Formatted time string
     */
    getFormattedTimeRemaining() {
        if (this.duration === null) return 'Active for Level';
        if (!this.isActive) return 'Inactive';
        
        const seconds = Math.ceil(this.timeRemaining / 1000);
        return `${seconds}s`;
    }
    
    /**
     * Check if this power-up can be purchased (override in subclasses for special conditions)
     * @param {PlayerShip} playerShip - The player ship
     * @param {number} playerTokens - Current player token balance
     * @returns {object} Purchase availability info
     */
    canPurchase(playerShip, playerTokens) {
        return {
            canPurchase: playerTokens >= this.cost && !this.isActive,
            reason: playerTokens < this.cost ? 'insufficient_tokens' : 
                   this.isActive ? 'already_active' : 'available'
        };
    }
    
    /**
     * Apply the specific effect (override in subclasses)
     * @param {PlayerShip} playerShip - The player ship
     * @returns {boolean} True if successfully applied
     */
    applyEffect(playerShip) {
        // Override in subclasses
        return true;
    }
    
    /**
     * Remove the specific effect (override in subclasses)
     * @param {PlayerShip} playerShip - The player ship
     * @returns {boolean} True if successfully removed
     */
    removeEffect(playerShip) {
        // Override in subclasses
        return true;
    }
    
    /**
     * Get power-up info for UI display
     * @returns {object} Power-up display information
     */
    getDisplayInfo() {
        return {
            id: this.id,
            type: this.type,
            cost: this.cost,
            duration: this.duration,
            description: this.description,
            isActive: this.isActive,
            timeRemaining: this.timeRemaining,
            timeRemainingPercentage: this.getTimeRemainingPercentage(),
            formattedTimeRemaining: this.getFormattedTimeRemaining(),
            icon: this.icon,
            color: this.color,
            glowColor: this.glowColor
        };
    }
}

/**
 * Extra Life power-up - Adds one extra life to the player
 */
export class ExtraLifePowerUp extends PowerUp {
    constructor() {
        super(
            'EXTRA_LIFE',
            GAME_CONFIG.POWER_UP_COSTS.EXTRA_LIFE,
            null, // Permanent effect
            'Gain an extra life to continue your journey'
        );
        
        this.icon = '❤️';
        this.color = '#ff4444';
        this.glowColor = '#ff8888';
    }
    
    applyEffect(playerShip) {
        playerShip.addLives(1);
        return true;
    }
    
    canPurchase(playerShip, playerTokens) {
        // Extra life can always be purchased if player has tokens
        return {
            canPurchase: playerTokens >= this.cost,
            reason: playerTokens < this.cost ? 'insufficient_tokens' : 'available'
        };
    }
}

/**
 * Spread Ammo power-up - Gives the player spread pattern ammunition
 */
export class SpreadAmmoPowerUp extends PowerUp {
    constructor() {
        super(
            'SPREAD_AMMO',
            GAME_CONFIG.POWER_UP_COSTS.SPREAD_AMMO,
            null, // Permanent effect until level completion
            'Fire projectiles in a spread pattern for better coverage'
        );
        
        this.icon = '🔥';
        this.color = '#ffaa00';
        this.glowColor = '#ffdd44';
    }
    
    applyEffect(playerShip) {
        if (playerShip.weaponSystem) {
            playerShip.weaponSystem.enableSpreadPattern(true);
            return true;
        }
        return false;
    }
    
    removeEffect(playerShip) {
        if (playerShip.weaponSystem) {
            playerShip.weaponSystem.enableSpreadPattern(false);
            return true;
        }
        return false;
    }
}

/**
 * Extra Wingman power-up - Adds a wingman ship that provides covering fire
 */
export class ExtraWingmanPowerUp extends PowerUp {
    constructor() {
        super(
            'EXTRA_WINGMAN',
            GAME_CONFIG.POWER_UP_COSTS.EXTRA_WINGMAN,
            null, // Permanent effect until level completion
            'Deploy a wingman ship to provide covering fire'
        );

        this.icon = '🚀';
        this.color = '#00ff88';
        this.glowColor = '#44ffaa';
        this.wingmanShip = null;
    }

    async applyEffect(playerShip) {
        try {
            // Import WingmanShip dynamically to avoid circular dependencies
            const { WingmanShip } = await import('../entities/WingmanShip.js');

            // Get the game object manager from the player ship's weapon system
            const gameObjectManager = playerShip.weaponSystem?.gameObjectManager;

            if (!gameObjectManager) {
                console.error('Cannot create wingman: GameObjectManager not available');
                return false;
            }

            // Create wingman ship at player's position with offset
            const wingmanPosition = playerShip.position.add(new Vector2(-40, 0));
            this.wingmanShip = new WingmanShip(
                wingmanPosition.x,
                wingmanPosition.y,
                playerShip,
                gameObjectManager
            );

            // Add wingman to game object manager
            gameObjectManager.add(this.wingmanShip);

            
            return true;
        } catch (error) {
            console.error('Failed to create wingman ship:', error);
            return false;
        }
    }

    removeEffect(playerShip) {
        if (this.wingmanShip) {
            this.wingmanShip.destroy();
            this.wingmanShip = null;
            
        }
        return true;
    }

    canPurchase(playerShip, playerTokens) {
        // Can't purchase if already active or if player doesn't have enough tokens
        return {
            canPurchase: playerTokens >= this.cost && !this.isActive,
            reason: playerTokens < this.cost ? 'insufficient_tokens' :
                   this.isActive ? 'already_active' : 'available'
        };
    }
}

/**
 * Reality Warp power-up - Allows player to transform the next level
 * Single unified version without tiers
 */
export class RealityWarpPowerUp extends PowerUp {
    constructor() {
        super(
            'REALITY_WARP',
            GAME_CONFIG.POWER_UP_COSTS.REALITY_WARP,
            null, // Permanent effect
            'Transform the next level with your imagination and reshape the battlefield'
        );
        
        this.icon = '🌌';
        this.color = '#6366f1';
        this.glowColor = '#818cf8';
    }
    
    async apply(playerShip) {
        if (this.isActive) {
            console.warn('RealityWarp is already active');
            return false;
        }

        this.isActive = true;
        this.appliedAt = Date.now();
        this.timeRemaining = null; // Power-ups last until level completion

        
        
        // Store the warp state for level transformation
        if (playerShip.realityWarpActive === undefined) {
            playerShip.realityWarpActive = false;
        }
        playerShip.realityWarpActive = true;

        // Note: The actual Reality Warp process is now triggered in GenieInterface.handlePowerUpPurchase
        // after collecting user input. This method just sets the flag on the player ship.

        return true;
    }
    
    remove(playerShip) {
        if (!this.isActive) {
            console.warn('RealityWarp is not active');
            return false;
        }
        
        this.isActive = false;
        this.timeRemaining = 0;
        
        // Clear the warp state from player ship
        if (playerShip.realityWarpActive === true) {
            playerShip.realityWarpActive = false;
        }
        
        
        return true;
    }
    
    canPurchase(playerShip, playerTokens) {
        // Reality warp can only be purchased if not already active
        return {
            canPurchase: playerTokens >= this.cost && !this.isActive,
            reason: playerTokens < this.cost ? 'insufficient_tokens' :
                   this.isActive ? 'already_active' : 'available'
        };
    }
}

/**
 * PowerUp factory for creating power-up instances
 */
export class PowerUpFactory {
    static createPowerUp(type) {
        switch (type) {
            case 'EXTRA_LIFE':
                return new ExtraLifePowerUp();
            case 'SPREAD_AMMO':
                return new SpreadAmmoPowerUp();
            case 'EXTRA_WINGMAN':
                return new ExtraWingmanPowerUp();
            case 'REALITY_WARP':
                return new RealityWarpPowerUp();
            case 'RAPID_FIRE':
                return new RapidFirePowerUp();
            case 'SHIELD':
                return new ShieldPowerUp();
            default:
                throw new Error(`Unknown power-up type: ${type}`);
        }
    }
    
    static getAllPowerUpTypes() {
        // RAPID_FIRE and SHIELD are excluded from this list because they should only be available as drops, not in the shop
        return ['EXTRA_LIFE', 'SPREAD_AMMO', 'EXTRA_WINGMAN', 'REALITY_WARP'];
    }
    
    static createAllPowerUps() {
        return this.getAllPowerUpTypes().map(type => this.createPowerUp(type));
    }
}

/**
 * Rapid Fire power-up - Doubles fire rate for 30 seconds
 */
export class RapidFirePowerUp extends PowerUp {
    constructor() {
        super(
            'RAPID_FIRE',
            0, // No cost, obtained from drops
            30000, // 30 second duration
            'Double fire rate for increased damage output'
        );
        
        this.icon = '⚡';
        this.color = '#ffff00';
        this.glowColor = '#ffaa00';
        this.originalFireRate = null;
    }
    
    applyEffect(playerShip) {
        if (!playerShip.weaponSystem) {
            return false;
        }
        
        // Check if player already has 2 rapid-fire power-ups active
        if (!playerShip.activePowerUps) {
            playerShip.activePowerUps = new Map();
        }
        
        const rapidFireCount = Array.from(playerShip.activePowerUps.values())
            .filter(powerUp => powerUp.type === 'RAPID_FIRE' && powerUp.isActive).length;
        
        if (rapidFireCount >= 2) {
            console.log(`RapidFire cannot be applied - player already has ${rapidFireCount} active rapid-fire power-ups (limit: 2)`);
            return false;
        }
        
        // Store original fire rate
        this.originalFireRate = playerShip.weaponSystem.fireRate;
        console.log(`RapidFire applied: original fireRate=${this.originalFireRate}ms, new fireRate=${this.originalFireRate / 2}ms`);
        
        // Double the fire rate (halve the cooldown time)
        playerShip.weaponSystem.fireRate = this.originalFireRate / 2;
        
        return true;
    }
    
    removeEffect(playerShip) {
        if (!playerShip.weaponSystem || this.originalFireRate === null) {
            return false;
        }
        
        // Restore original fire rate
        playerShip.weaponSystem.fireRate = this.originalFireRate;
        console.log(`RapidFire removed: restored fireRate=${this.originalFireRate}ms`);
        this.originalFireRate = null;
        
        return true;
    }
    
    canPurchase(playerShip, playerTokens) {
        // Rapid fire cannot be purchased, only obtained from drops
        return {
            canPurchase: false,
            reason: 'drop_only'
        };
    }
}

/**
 * Shield power-up - Reflects enemy fire back at enemies for 30 seconds
 */
export class ShieldPowerUp extends PowerUp {
    constructor() {
        super(
            'SHIELD',
            0, // No cost, obtained from drops
            30000, // 30 second duration
            'Shield that reflects enemy fire back at enemies'
        );
        
        this.icon = '🛡️';
        this.color = '#00ffff';
        this.glowColor = '#88ffff';
        this.shieldActive = false;
    }
    
    applyEffect(playerShip) {
        if (!playerShip.weaponSystem) {
            return false;
        }
        
        // Check if player already has 2 shield power-ups active
        if (!playerShip.activePowerUps) {
            playerShip.activePowerUps = new Map();
        }
        
        const shieldCount = Array.from(playerShip.activePowerUps.values())
            .filter(powerUp => powerUp.type === 'SHIELD' && powerUp.isActive).length;
        
        if (shieldCount >= 2) {
            console.log(`Shield cannot be applied - player already has ${shieldCount} active shield power-ups (limit: 2)`);
            return false;
        }
        
        // Activate shield
        this.shieldActive = true;
        playerShip.hasShield = true;
        console.log('Shield power-up applied: player now has reflective shield');
        
        return true;
    }
    
    removeEffect(playerShip) {
        // Deactivate shield
        this.shieldActive = false;
        playerShip.hasShield = false;
        console.log('Shield power-up removed: player no longer has reflective shield');
        
        return true;
    }
    
    canPurchase(playerShip, playerTokens) {
        // Shield cannot be purchased, only obtained from drops
        return {
            canPurchase: false,
            reason: 'drop_only'
        };
    }
}
