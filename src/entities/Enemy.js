import { GameObject } from '../utils/GameObject.js';
import { Vector2 } from '../utils/Vector2.js';
import { GameMath } from '../utils/GameMath.js';
import { ENEMY_TYPES, GAME_CONFIG } from '../config/gameConfig.js';

/**
 * Enemy base class with various movement patterns and behaviors
 * Supports different enemy types with environmental effectiveness modifiers
 */
export class Enemy extends GameObject {
    constructor(x = 0, y = 0, type = ENEMY_TYPES.AIR) {
        super(x, y);
        
        // Enemy properties
        this.type = type;
        this.maxHealth = this.getTypeMaxHealth(type);
        this.health = this.maxHealth;
        this.scoreValue = this.getTypeScoreValue(type);
        this.speed = this.getTypeBaseSpeed(type);
        this.isDestroyed = false;
        
        // Movement pattern
        this.movementPattern = 'straight';
        this.movementPatternOptions = {};
        this.patternTime = 0;
        
        // Enemy-specific properties
        this.collisionRadius = this.getTypeSize(type);
        this.color = this.getColorByType();
        this.damage = this.getTypeDamage(type);
        
        // Sprite properties
        this.sprite = null;
        this.useSprite = false;
        this.spriteScale = 2.0;
        
        // Special behavior properties
        this.specialBehaviorCooldown = 0;
        this.specialBehaviorInterval = this.getTypeSpecialBehaviorInterval(type);

        // Formation properties
        this.formationCenter = null;
        this.formationOffset = null;
        this.inFormation = false;

        // Dive attack properties (Space Invaders style)
        this.formationState = 'formation'; // 'formation' or 'dive'
        this.formationIndex = 0;
        this.diveStartTime = 0;
        this.diveReturnTime = 0;
        this.originalFormationCenter = null;
        this.originalFormationOffset = null;
        
        // Initialize
        this.updateCollisionBounds();
    }
    
    /**
     * Get color based on enemy type
     * @returns {string} Color for the enemy type
     */
    getColorByType() {
        switch (this.type) {
            case ENEMY_TYPES.WATER: return '#3498db';
            case ENEMY_TYPES.FIRE: return '#e74c3c';
            case ENEMY_TYPES.AIR: return '#ecf0f1';
            case ENEMY_TYPES.EARTH: return '#8b4513';
            case ENEMY_TYPES.CRYSTAL: return '#9b59b6';
            case ENEMY_TYPES.SHADOW: return '#2c3e50';
            default: return '#95a5a6';
        }
     }
    
    /**
     * Get size based on enemy type
     * @param {string} type - Enemy type
     * @returns {number} Size (collision radius) for the enemy type
     */
    getTypeSize(type) {
        switch (type) {
            case ENEMY_TYPES.WATER: return 20;  // Water enemies - medium size, balanced
            case ENEMY_TYPES.FIRE: return 14;   // Fire enemies - very small, hard to hit
            case ENEMY_TYPES.AIR: return 16;    // Air enemies - small, agile
            case ENEMY_TYPES.EARTH: return 28;  // Earth enemies - very large, easy to hit but tanky
            case ENEMY_TYPES.CRYSTAL: return 24; // Crystal enemies - large, special abilities
            case ENEMY_TYPES.SHADOW: return 12;  // Shadow enemies - extremely small, very hard to hit
            default: return 20; // Default size
        }
    }
    
    /**
     * Get damage output based on enemy type
     * @param {string} type - Enemy type
     * @returns {number} Damage output for the enemy type
     */
    getTypeDamage(type) {
        switch (type) {
            case ENEMY_TYPES.WATER: return 12;  // Water enemies - moderate damage, balanced
            case ENEMY_TYPES.FIRE: return 18;   // Fire enemies - high damage, glass cannon
            case ENEMY_TYPES.AIR: return 10;    // Air enemies - low damage, focused on evasion
            case ENEMY_TYPES.EARTH: return 25;  // Earth enemies - very high damage, slow but powerful
            case ENEMY_TYPES.CRYSTAL: return 15; // Crystal enemies - moderate damage with special abilities
            case ENEMY_TYPES.SHADOW: return 20;  // Shadow enemies - high damage, hit and run tactics
            default: return 12; // Default damage
        }
    }
    
    /**
     * Get special behavior interval based on enemy type
     * @param {string} type - Enemy type
     * @returns {number} Interval in milliseconds for special behaviors
     */
    getTypeSpecialBehaviorInterval(type) {
        switch (type) {
            case ENEMY_TYPES.WATER: return 5000;  // Water enemies - periodic behavior
            case ENEMY_TYPES.FIRE: return 3000;   // Fire enemies - frequent aggressive behavior
            case ENEMY_TYPES.AIR: return 4000;    // Air enemies - moderate behavior frequency
            case ENEMY_TYPES.EARTH: return 6000;  // Earth enemies - slow but powerful behaviors
            case ENEMY_TYPES.CRYSTAL: return 7000; // Crystal enemies - rare but powerful special attacks
            case ENEMY_TYPES.SHADOW: return 3500;  // Shadow enemies - frequent stealthy behaviors
            default: return 5000; // Default interval
        }
    }
     
    /**
     * Get base speed based on enemy type
     * @param {string} type - Enemy type
     * @returns {number} Base speed for the enemy type
     */
    getTypeBaseSpeed(type) {
        switch (type) {
            case ENEMY_TYPES.WATER: return 7.0;  // Water enemies - moderate speed, balanced (doubled)
            case ENEMY_TYPES.FIRE: return 11.0;  // Fire enemies - very fast but fragile (doubled)
            case ENEMY_TYPES.AIR: return 10.0;   // Air enemies - fast and agile (doubled)
            case ENEMY_TYPES.EARTH: return 5.0;  // Earth enemies - very slow but tanky (doubled)
            case ENEMY_TYPES.CRYSTAL: return 6.0; // Crystal enemies - slow but with special abilities (doubled)
            case ENEMY_TYPES.SHADOW: return 12.0; // Shadow enemies - extremely fast but low health (doubled)
            default: return 8.0; // Default speed (doubled)
        }
    }
    /**
     * Get max health based on enemy type
     * @param {string} type - Enemy type
     * @returns {number} Max health for the enemy type
     */
    getTypeMaxHealth(type) {
        switch (type) {
            case ENEMY_TYPES.WATER: return 50;  // Water enemies - 2 shots to kill
            case ENEMY_TYPES.FIRE: return 25;   // Fire enemies - 1 shot to kill
            case ENEMY_TYPES.AIR: return 25;    // Air enemies - 1 shot to kill
            case ENEMY_TYPES.EARTH: return 75;  // Earth enemies - 3 shots to kill
            case ENEMY_TYPES.CRYSTAL: return 75; // Crystal enemies - 3 shots to kill
            case ENEMY_TYPES.SHADOW: return 50;  // Shadow enemies - 2 shots to kill
            default: return 25; // Default health - 1 shot to kill
        }
    }
    
    /**
     * Get score value based on enemy type
     * @param {string} type - Enemy type
     * @returns {number} Score value for the enemy type
     */
    getTypeScoreValue(type) {
        switch (type) {
            case ENEMY_TYPES.WATER: return 15;  // Water enemies - balanced points for balanced stats
            case ENEMY_TYPES.FIRE: return 20;   // Fire enemies - high points for being fast and fragile
            case ENEMY_TYPES.AIR: return 18;    // Air enemies - moderate-high points for speed and evasion
            case ENEMY_TYPES.EARTH: return 25;  // Earth enemies - high points for being tanky but slow
            case ENEMY_TYPES.CRYSTAL: return 30; // Crystal enemies - very high points for special abilities
            case ENEMY_TYPES.SHADOW: return 35;  // Shadow enemies - highest points for extreme speed and low health
            default: return 15; // Default points
        }
    }
    
    
    /**
     * Update enemy state
     * @param {number} deltaTime - Time elapsed since last update
     * @param {Vector2} playerPosition - Player's current position
     */
    update(deltaTime, playerPosition = null) {
        if (!this.active) return;
        
        super.update(deltaTime);
        
        // Update special behavior cooldown
        this.specialBehaviorCooldown += deltaTime;
        
        // Execute special behaviors if cooldown is complete
        if (this.specialBehaviorCooldown >= this.specialBehaviorInterval) {
            this.executeSpecialBehavior(playerPosition);
            this.specialBehaviorCooldown = 0;
        }
        
        // Update movement pattern
        this.updateMovementPattern(deltaTime, playerPosition);
        
        // Check if enemy is out of bounds
        this.checkBounds();
    }
    
    /**
     * Execute special behavior based on enemy type
     * @param {Vector2} playerPosition - Player's current position
     */
    executeSpecialBehavior(playerPosition) {
        if (!playerPosition) return;
        
        switch (this.type) {
            case ENEMY_TYPES.WATER:
                this.executeWaterBehavior(playerPosition);
                break;
            case ENEMY_TYPES.FIRE:
                this.executeFireBehavior(playerPosition);
                break;
            case ENEMY_TYPES.AIR:
                this.executeAirBehavior(playerPosition);
                break;
            case ENEMY_TYPES.EARTH:
                this.executeEarthBehavior(playerPosition);
                break;
            case ENEMY_TYPES.CRYSTAL:
                this.executeCrystalBehavior(playerPosition);
                break;
            case ENEMY_TYPES.SHADOW:
                this.executeShadowBehavior(playerPosition);
                break;
        }
    }
    
    /**
     * Water enemy special behavior: Speed boost and direction change
     * @param {Vector2} playerPosition - Player's current position
     */
    executeWaterBehavior(playerPosition) {
        // Water enemies get a temporary speed boost and change direction toward player
        const originalSpeed = this.speed;
        this.speed *= 1.5;
        
        const direction = this.directionTo(playerPosition);
        this.velocity = direction.multiply(this.speed);
        
        // Reset speed after a short duration
        setTimeout(() => {
            this.speed = originalSpeed;
        }, 1000);
    }
    
    /**
     * Fire enemy special behavior: Aggressive dash toward player
     * @param {Vector2} playerPosition - Player's current position
     */
    executeFireBehavior(playerPosition) {
        // Fire enemies dash aggressively toward the player
        const direction = this.directionTo(playerPosition);
        const dashSpeed = this.speed * 2.5;
        this.velocity = direction.multiply(dashSpeed);
        
        // Set a short movement pattern change
        this.movementPattern = 'player_follow';
        this.movementPatternOptions = { duration: 1000 };
        
        // Reset after dash
        setTimeout(() => {
            this.movementPattern = 'straight';
            this.movementPatternOptions = {};
        }, 1000);
    }
    
    /**
     * Air enemy special behavior: Evasive maneuver
     * @param {Vector2} playerPosition - Player's current position
     */
    executeAirBehavior(playerPosition) {
        // Air enemies perform evasive maneuvers
        const direction = this.directionTo(playerPosition);
        const perpendicular = new Vector2(-direction.y, direction.x);
        
        // Move perpendicular to player direction for evasion
        this.velocity = perpendicular.multiply(this.speed * 1.8);
        
        // Set sine wave pattern for evasion
        this.movementPattern = 'sine';
        this.movementPatternOptions = { amplitude: 100 };
        
        // Reset after evasion
        setTimeout(() => {
            this.movementPattern = 'straight';
            this.movementPatternOptions = {};
        }, 1500);
    }
    
    /**
     * Earth enemy special behavior: Temporary speed reduction
     * @param {Vector2} playerPosition - Player's current position
     */
    executeEarthBehavior(playerPosition) {
        // Earth enemies move slower temporarily
        const originalSpeed = this.speed;
        this.speed *= 0.5; // Move slower

        // Reset after a duration
        setTimeout(() => {
            this.speed = originalSpeed; // Restore normal speed
        }, 2000);
    }
    
    /**
     * Crystal enemy special behavior: Split into smaller fragments
     * @param {Vector2} playerPosition - Player's current position
     */
    executeCrystalBehavior(playerPosition) {
        // Crystal enemies emit a damaging pulse in all directions
        // This would typically create projectile objects, but for now we'll just log it
        
        // Crystal enemies also get a brief speed boost
        const originalSpeed = this.speed;
        this.speed *= 1.3;
        
        // Reset speed after a short duration
        setTimeout(() => {
            this.speed = originalSpeed;
        }, 1500);
    }
    
    /**
     * Shadow enemy special behavior: Teleport/blink
     * @param {Vector2} playerPosition - Player's current position
     */
    executeShadowBehavior(playerPosition) {
        // Shadow enemies teleport to a random position near the player
        const angle = Math.random() * Math.PI * 2;
        const distance = 100 + Math.random() * 100;
        
        const newX = playerPosition.x + Math.cos(angle) * distance;
        const newY = playerPosition.y + Math.sin(angle) * distance;
        
        // Ensure new position is within bounds
        const boundedX = Math.max(50, Math.min(GAME_CONFIG.CANVAS_WIDTH - 50, newX));
        const boundedY = Math.max(50, Math.min(GAME_CONFIG.CANVAS_HEIGHT - 50, newY));
        
        this.position = new Vector2(boundedX, boundedY);
        
        // Brief pause after teleport
        this.velocity = new Vector2(0, 0);
        
        // Resume movement after a brief pause
        setTimeout(() => {
            this.movementPattern = 'straight';
        }, 500);
    }
    
    /**
     * Update movement pattern
     * @param {number} deltaTime - Time elapsed since last update
     * @param {Vector2} playerPosition - Player's current position
     */
    updateMovementPattern(deltaTime, playerPosition) {
        this.patternTime += deltaTime;

        // Handle dive attack state differently
        if (this.formationState === 'dive') {
            // Diving enemies use their velocity directly (set by EnemyManager)
            // No need to override here, just let the dive system handle movement
            return;
        }

        switch (this.movementPattern) {
            case 'straight':
                this.velocity.y = this.speed;
                break;

            case 'sine':
                this.velocity.y = this.speed;
                this.velocity.x = Math.sin(this.patternTime / 200) * (this.movementPatternOptions.amplitude || 50);
                break;

            case 'player_follow':
                if (playerPosition) {
                    const direction = this.directionTo(playerPosition);
                    this.velocity = direction.multiply(this.speed);
                }
                break;

            case 'formation':
                if (this.formationCenter && this.formationOffset) {
                    // Calculate target position based on formation center and offset
                    const targetPosition = this.formationCenter.add(this.formationOffset);

                    // Calculate distance to target position
                    const distanceToTarget = this.position.distance(targetPosition);

                    // If far from target, move towards it with formation speed
                    if (distanceToTarget > 5) {
                        const direction = this.directionTo(targetPosition);
                        // Use formation speed for more coordinated movement
                        const formationSpeed = this.speed * 1.2; // Slightly faster to maintain formation
                        this.velocity = direction.multiply(formationSpeed);
                    } else {
                        // Close to target, match formation movement more precisely
                        // This helps maintain relative positions within the formation
                        this.velocity = this.velocity.multiply(0.9); // Gradual slowdown
                    }
                }
                break;

            // Add more patterns as needed
        }
    }
    
    /**
     * Set movement pattern for the enemy
     * @param {string} pattern - Movement pattern type
     * @param {object} options - Pattern options
     */
    setMovementPattern(pattern, options = {}) {
        this.movementPattern = pattern;
        this.movementPatternOptions = options;
        this.patternTime = 0;
    }
    
    /**
     * Check if enemy is out of bounds and mark for destruction
     */
    checkBounds() {
        const bounds = {
            left: -50,
            right: GAME_CONFIG.CANVAS_WIDTH + 50,
            top: -50,
            bottom: GAME_CONFIG.CANVAS_HEIGHT + 50
        };
        
        if (this.isOutOfBounds(bounds)) {
            this.destroy();
        }
    }
    
    /**
     * Take damage
     * @param {number} damage - Amount of damage to take
     * @returns {object} Damage result information
     */
    takeDamage(damage) {
        const actualDamage = Math.max(0, damage); // Ensure damage is not negative
        this.health -= actualDamage;

        const result = {
            damageTaken: actualDamage,
            destroyed: false,
            scoreValue: 0
        };

        if (this.health <= 0) {
            this.isDestroyed = true;
            this.destroy();
            result.destroyed = true;
            result.scoreValue = this.scoreValue;
        }

        return result;
    }
    
    /**
     * Render the enemy
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} interpolation - Interpolation value for smooth rendering
     */
    render(ctx, interpolation = 0) {
        if (!this.visible) return;
        
        super.render(ctx, interpolation);
        
        // Interpolated position for smooth rendering
        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));
        
        ctx.save();
        ctx.translate(renderPos.x, renderPos.y);
        ctx.rotate(this.rotation);
        
        // Draw enemy - use sprite if available, otherwise fall back to circle
        const sprite = this.getSprite();
        if (sprite) {
            // Draw sprite with bloom effect
            const spriteSize = this.collisionRadius * 2 * this.spriteScale;
            
            // Apply shadow blur for bloom effect (makes sprite brighter for post-processing bloom)
            ctx.shadowColor = this.color;
            ctx.shadowBlur = 10;
            
            ctx.drawImage(
                sprite,
                -spriteSize / 2,
                -spriteSize / 2,
                spriteSize,
                spriteSize
            );
            
            ctx.shadowBlur = 0;
        } else {
            // Fallback rendering removed - only sprites will be used
            // No circle will be drawn as fallback
        }

        // Draw health bar only when damaged
        if (this.health < this.maxHealth) {
            const healthPercentage = this.health / this.maxHealth;
            const barWidth = this.collisionRadius * 2;
            const healthBarWidth = barWidth * healthPercentage;

            // Red background
            ctx.fillStyle = '#ff0000';
            ctx.fillRect(-this.collisionRadius, -this.collisionRadius - 10, barWidth, 4);

            // Green health portion
            ctx.fillStyle = '#00ff00';
            ctx.fillRect(-this.collisionRadius, -this.collisionRadius - 10, healthBarWidth, 4);
        }
        
        ctx.restore();
    }
    
    /**
     * Set formation target for enemy
     * @param {Vector2} center - Formation center position
     * @param {Vector2} offset - Offset from formation center
     */
    setFormationTarget(center, offset) {
        this.formationCenter = center;
        this.formationOffset = offset;
        this.inFormation = true;
        this.movementPattern = 'formation';
        
        // Store the initial offset to maintain relative position
        if (!this.initialFormationOffset) {
            this.initialFormationOffset = offset.clone();
        }
    }
    
    /**
     * Reset enemy state for object pooling
     */
    reset() {
        super.reset();
        // Reset health to full for the enemy type
        this.maxHealth = this.getTypeMaxHealth(this.type);
        this.health = this.maxHealth;
        this.scoreValue = this.getTypeScoreValue(this.type);
        this.speed = this.getTypeBaseSpeed(this.type);
        this.isDestroyed = false;
        this.movementPattern = 'straight';
        this.movementPatternOptions = {};
        this.patternTime = 0;
        this.color = this.getColorByType();
        this.collisionRadius = this.getTypeSize(this.type);
        this.damage = this.getTypeDamage(this.type);
        this.specialBehaviorCooldown = 0;
        this.specialBehaviorInterval = this.getTypeSpecialBehaviorInterval(this.type);
        this.formationCenter = null;
        this.formationOffset = null;
        this.initialFormationOffset = null;
        this.inFormation = false;

        // Reset dive attack properties
        this.formationState = 'formation';
        this.formationIndex = 0;
        this.diveStartTime = 0;
        this.diveReturnTime = 0;
        this.originalFormationCenter = null;
        this.originalFormationOffset = null;
    }

    /**
     * Get sprite for this enemy type from the game engine
     * @returns {HTMLImageElement|null} Sprite image or null if not available
     */
    getSprite() {
        if (window.gameEngine && window.gameEngine.enemySprites) {
            const sprite = window.gameEngine.enemySprites.get(this.type);
            if (sprite && sprite.complete) {
                return sprite;
            }
        }
        return null;
    }
}